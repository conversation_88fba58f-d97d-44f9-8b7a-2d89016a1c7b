<?php

namespace App\Services;

use AlibabaCloud\SDK\Ocrapi\V20210707\Models\RecognizeBankCardRequest;
use AlibabaCloud\SDK\Ocrapi\V20210707\Ocrapi;
use \Exception;
use AlibabaCloud\Tea\Exception\TeaError;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\SDK\Ocrapi\V20210707\Models\RecognizeIdcardRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use GuzzleHttp\Psr7\Stream;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use AlibabaCloud\SDK\Ocrapi\V20210707\Models\RecognizeChinesePassportRequest;
use Throwable;

class OcrService
{
    const BANK_CARD_TYPE_DC = 'DC'; //银行卡卡种，储蓄卡

    /**
     * 使用AK&SK初始化账号Client
     * @return Ocrapi Client
     */
    public static function createClient()
    {
        $config = new Config([
            "accessKeyId" => env("ALIBABA_CLOUD_ACCESS_KEY_ID"),
            "accessKeySecret" => env("ALIBABA_CLOUD_ACCESS_KEY_SECRET")
        ]);
        $config->endpoint = "ocr-api.cn-hangzhou.aliyuncs.com";
        return new Ocrapi($config);
    }

    //识别中国身份证 https://help.aliyun.com/zh/ocr/developer-reference/api-ocr-api-2021-07-07-recognizeidcard
    public static function idCard($filePath)
    {
        $return = [];
        $fileAPath = Storage::disk('public')->path('') . $filePath;
        $client = self::createClient();
        $fileStream = new Stream(fopen($fileAPath, 'r'));
        $recognizeIdcardRequest = new RecognizeIdcardRequest(['body' => $fileStream]);
        $runtime = new RuntimeOptions([]);
        try {
            //捕获 PHP 错误
            set_error_handler(function($errno, $errstr, $errfile, $errline) {
                $errorInfo = [
                    'error' => true,
                    'error_message' => "Plugin Error: $errstr in $errfile on line $errline",
                    'error_code' => $errno,
                    'error_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
                ];
                throw new Exception($errorInfo['error_message']);
            });

            try {
                $res = $client->recognizeIdcardWithOptions($recognizeIdcardRequest, $runtime);
            } catch (Throwable $e) {
                $errorInfo = [
                    'error' => true,
                    'error_message' => "Plugin execution failed: " . $e->getMessage(),
                    'error_code' => $e->getCode() ?: 'PLUGIN_ERROR',
                    'error_trace' => $e->getTraceAsString()
                ];
                throw new Exception($errorInfo['error_message']);
            } finally {
                // 恢复默认的错误处理器
                restore_error_handler();
            }

            $res = $res->toMap();
            $resJson = $res['body']['Data'];
            $res = json_decode($res['body']['Data'], true);
            if (isset($res['data']['face'])) {
                $data = $res['data']['face']['data'];
                $return = [
                    'name' => $data['name'],
                    'sex' => $data['sex'],
                    'ethnicity' => $data['ethnicity'],
                    'birthdate' => $data['birthDate'],
                    'address' => $data['address'],
                    'id_number' => $data['idNumber'],
                    'face_response' => $resJson,
                    'face_file' => $filePath,
                ];
            } else if (isset($res['data']['back'])) {
                $data = $res['data']['back']['data'];
                $return = [
                    'issue_authority' => $data['issueAuthority'],
                    'valid_period' => $data['validPeriod'],
                    'back_response' => $resJson,
                    'back_file' => $filePath,
                ];
            }
        } catch (Throwable $error) {
            $errorInfo = [
                'error' => true,
                'error_message' => $error->getMessage(),
                'error_code' => $error->getCode() ?: 'PLUGIN_ERROR',
                'error_trace' => $error->getTraceAsString()
            ];
            debugLog("错误信息：" . json_encode($errorInfo));
            $return = $errorInfo;
        }
        return $return;
    }

    //识别中国护照 https://help.aliyun.com/zh/ocr/developer-reference/api-ocr-api-2021-07-07-recognizechinesepassport
    public static function passportCn($filePath)
    {
        $return = [];
        $fileAPath = Storage::disk('public')->path('') . $filePath;
        $client = self::createClient();
        $fileStream = new Stream(fopen($fileAPath, 'r'));
        $recognizeChinesePassportRequest = new RecognizeChinesePassportRequest(['body' => $fileStream]);
        $runtime = new RuntimeOptions([]);
        try {
            $res = $client->recognizeChinesePassportWithOptions($recognizeChinesePassportRequest, $runtime);
            if (!$res) {
                throw new Exception("识别结果为空");
            }

            try {
                $res = $res->toMap();
                $resJson = $res['body']['Data'];
                $res = json_decode($res['body']['Data'], true);
            } catch (Exception $e) {
                throw new Exception("解析识别结果失败: " . $e->getMessage());
            }

            if ($res['data']) {
                $data = $res['data'];
                $return = [
                    'passport_type' => $data['passportType'],
                    'country_code' => $data['countryCode'],
                    'passport_number' => $data['passportNumber'],
                    'name_en' => $data['nameEn'],
                    'name' => $data['name'],
                    'sex' => $data['sex'],
                    'birth_place' => $data['birthPlace'],
                    'nationality' => $data['nationality'],
                    'issue_place' => $data['issuePlace'],
                    'issue_authority' => $data['issueAuthority'],
                    'mrz_line1' => $data['mrzLine1'],
                    'mrz_line2' => $data['mrzLine2'],
                    'valid_to_date' => $data['validToDate'],
                    'birth_date' => $data['birthDate'],
                    'issue_date' => $data['issueDate'],
                    'response' => $resJson,
                ];
            } else {
                throw new Exception("未识别到护照信息");
            }
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            Log::error('OcrService_passportCn: ' . $error->message);
            $return = [
                'error' => true,
                'error_message' => $error->message,
                'error_code' => $error->code ?? 'UNKNOWN_ERROR',
                'error_trace' => $error->getTraceAsString()
            ];
        }
        return $return;
    }

    //识别国内银行卡 https://help.aliyun.com/zh/ocr/developer-reference/api-ocr-api-2021-07-07-recognizebankcard
    public static function bankCardCn($url)
    {
        $return = [];
        $client = self::createClient();
        $recognizeBankCardRequest = new RecognizeBankCardRequest(['url' => $url]);
        $runtime = new RuntimeOptions([]);
        try {
            //捕获 PHP 错误
            set_error_handler(function($errno, $errstr, $errfile, $errline) {
                $errorInfo = [
                    'error' => true,
                    'error_message' => "Plugin Error: $errstr in $errfile on line $errline",
                    'error_code' => $errno,
                    'error_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
                ];
                throw new Exception($errorInfo['error_message']);
            });
            try {
                $res = $client->recognizeBankCardWithOptions($recognizeBankCardRequest, $runtime);
            } catch (Throwable $e) {
                $errorInfo = [
                    'error' => true,
                    'error_message' => "Plugin execution failed: " . $e->getMessage(),
                    'error_code' => $e->getCode() ?: 'PLUGIN_ERROR',
                    'error_trace' => $e->getTraceAsString()
                ];
                throw new Exception($errorInfo['error_message']);
            } finally {
                // 恢复默认的错误处理器
                restore_error_handler();
            }
            $res = $res->toMap();
            Log::info('识别银行卡返回结果:' , $res);
            $resJson = $res['body']['Data'];
            $res = json_decode($res['body']['Data'], true);
            //Log::info('识别银行卡返回结果--处理后:' , $res);
            if ($res['data']) {
                $data = $res['data'];
                $return = [
                    'card_type' => $data['cardType'],
                    'bank_name' => $data['bankName'],
                    'card_number' => $data['cardNumber'],
                    'valid_to_date' => $data['validToDate'],
                    'response' => $resJson,
                ];
            }
        } catch (Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            Log::error('OcrService_bankCardCn: ' . $error->message);
        }
        return $return;
    }
}