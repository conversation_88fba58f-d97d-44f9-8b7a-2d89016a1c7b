<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentModel extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'cna_payment';
    protected $guarded = [];
    protected $primaryKey = 'id';

    const PAY_TYPE_WECHAT = 1;//微信
    const PAY_TYPE_ALIPAY = 2;//支付宝
    const PAY_TYPE_ONLINE_BANK = 3;//网银
    const PAY_TYPE_EXEMPT = 4;//豁免


    const TYPE_INCOME =1;//业务收入
    const TYPE_EXPENSE = 2;//业务支出
    const TYPE_EXEMPT =3;//豁免
    const TYPE_COMMISSION_INCOME =4;//津贴收入
    const TYPE_COMMISSION_EXPENSE =5;//津贴支出

    // 所有有效的支付类型
    const VALID_TYPES = [
        self::TYPE_INCOME,
        self::TYPE_EXPENSE,
        self::TYPE_EXEMPT,
        self::TYPE_COMMISSION_INCOME,
        self::TYPE_COMMISSION_EXPENSE
    ];

    const INCOME_TYPE = [self::TYPE_INCOME,self::TYPE_COMMISSION_INCOME];
    const EXPENSE_TYPE = [self::TYPE_EXPENSE,self::TYPE_EXEMPT,self::TYPE_COMMISSION_EXPENSE];

    const STATUS_NO_RECEIVED = 1; // 未到账
    const STATUS_RECEIVED = 2; // 已到账
    const STATUS_OUTPUT = 3; // 已出账
    const STATUS_EXEMPT = 4;//已豁免

    protected $appends = [
        'status',
        'profileName',
        'profilePartnerCode',
        'profileNRIC',
        'groupId',
        'teamRankName',
        'sonName',
        'divideTypeName'
    ];

    /**
     * 项目
     */
    public function project()
    {
        return $this->belongsTo(ProjectCategoryModel::class, 'projectId', 'projectCategoriesID');
    }

    /**
     * 项目明细
     */
    public function projectContent()
    {
        return $this->belongsTo(ProjectServiceModel::class, 'detailId');
    }

    /**
     * 合伙人
     */
    public function profile()
    {
        return $this->belongsTo(ProfileInfoModel::class, 'profileID');
    }


    /**
     * 状态
     * @return void
     */
    public function getStatusAttribute()
    {
        /*$createtime = $this->attributes['createtime']??'';
        $paytime = $this->attributes['paytime']??'';
        if ($createtime && $paytime) {
            return 1;
        }
        return 0;*/
        if ($this->attributes['type'] == self::TYPE_INCOME) { // 对用户而言的支出
            $status = self::STATUS_OUTPUT;
        } elseif ($this->attributes['type'] == self::TYPE_EXEMPT) {//豁免业务
            $status = self::STATUS_EXEMPT;
        } else { // 客户收入
            if (isset($this->attributes['check']) && $this->attributes['check'] == 2) { // 发票已审核
                $status = self::STATUS_RECEIVED;
            } else {
                $status = self::STATUS_NO_RECEIVED;
            }
        }

        return $status;
    }

    /**
     * 合伙人名称
     * @return mixed|null
     */
    public function getProfileNameAttribute()
    {
        return $this->profile()->value('profileName');
    }

    /**
     * 合伙人编码
     * @return mixed|null
     */
    public function getProfilePartnerCodeAttribute()
    {
        return $this->profile()->value('profilePartnerCode');
    }

    /**
     * 身份证号
     * @return mixed|null
     */
    public function getProfileNRICAttribute()
    {
        return $this->profile()->value('profileNRIC');
    }

    /**
     * 合伙人角色
     * @return mixed|null
     */
    public function getGroupIdAttribute()
    {

        return ProfileInfoModel::getGroupById($this->attributes['profileID']);

    }

    /**
     * 三三制等级
     * @return mixed|null
     */
    public function getTeamRankNameAttribute()
    {
        $team_rank = $this->profile()->value('team_rank');
        return $team_rank > 0 ? TeamRank::where('id', $team_rank)->value('job') : '';
    }

    /**
     * 旗下合伙人名称
     * @return mixed|null
     */
    public function getSonNameAttribute()
    {
        $son_id = $this->attributes['son_id'] ?? '';
        return $son_id > 0 ? ProfileInfoModel::where('profileID', $son_id)->value('profileName') : '';
    }

    /**
     * 分类类型
     * @return mixed|null
     */
    public function getDivideTypeNameAttribute()
    {
        $this->attributes['divide_type'] = $this->attributes['divide_type'] ?? '';
        if ($this->attributes['divide_type'] == 1) {
            return '收益分成';
        } else if ($this->attributes['divide_type'] == 2) {
            return '管理津贴';
        } else if ($this->attributes['divide_type'] == 3) {
            return '运营津贴';
        } else {
            return '业务款项';
        }
    }

    /**
     * 累计已付
     * @param $end_date
     * @param $profile_id
     * @return void
     */
    public static function getPutTotal($profile_id)
    {
        $put_total = self::where('profileID', $profile_id)->where('type', 1)->sum('put_amount');
        return $put_total;
    }

    /**
     * 期初余额
     * @return void
     */
    public static function startBalance($start_date, $profile_id)
    {
        // 应付
        $totalMustPay = PaymentModel::query()
            ->where('type', 1)
            ->select('profileID', $profile_id)
            ->sum('divide_profit');

        // 本期已付
        $totalPaid = PaymentModel::query()
            ->where('type', 1)
            ->select('profileID', $profile_id)
            ->sum('put_amount');

        return bcsub($totalMustPay, $totalPaid, 2);
    }


    /**
     * 生成单号(SKD+主体公司字母代码+数字||FKD+主体公司字母代码+数字)
     * @param $code 公司主体编号
     * @param $type 应收或应付 (1应收; 2应付)
     * @return string
     */
    public static function createPaymentNumber($code, $type)
    {
        $mainId = PaymentMainModel::query()->where('code', $code)->value('id');
        
        // 检查主体公司是否存在
        if (!$mainId) {
            throw new \Exception('主体公司不存在: ' . $code);
        }

        // 当前最大编码数
        $curMaxCode = self::query()
            ->where('type', $type)
            ->where('main', $mainId)
            ->orderBy('paymentNumber', 'desc')
            ->limit(1)
            ->value('paymentNumber');
        if ($curMaxCode) {
            $number = preg_replace("/[^0-9]/", "", $curMaxCode);
            $number += 1;

        } else {
            $number = 1000000000;
        }

        if ( in_array($type,self::INCOME_TYPE) ) { // 对于用户应付
            $pre = 'FKD';
        } else if ( in_array($type,self::EXPENSE_TYPE) ) { // 对于用户应收
            $pre = 'SKD';
        } else {
            throw new \Exception('无效的支付类型: ' . $type);
        }

        $paymentNumber = $pre . $code . $number;

        return $paymentNumber;
    }

    /**
     * 计算同比昨日增加的收入和支出
     * @param $profileID
     * @return array
     */
    public static function getTodayIncomeAndExpense($profileID)
    {
        // 获取今天的收入和支出
        $todayIncome = self::where('type', 2)
            ->where('profileID', $profileID)
            ->whereDate('createtime', Carbon::today())
            ->sum('divide_profit');

        $todayExpense = self::where('type', 1)
            ->where('profileID', $profileID)
            ->whereDate('createtime', Carbon::today())
            ->sum('fee');

        // 获取昨天的收入和支出
        $yesterday = Carbon::yesterday();
        $startOfYesterday = $yesterday->copy()->startOfDay()->toDateString();
        $endOfYesterday = $yesterday->copy()->endOfDay()->toDateString();

        $yesterdayIncome = self::where('type', 2)
            ->where('profileID', $profileID)
            ->whereBetween('createtime', [$startOfYesterday, $endOfYesterday])
            ->sum('divide_profit');

        $yesterdayExpense = self::where('type', 1)
            ->where('profileID', $profileID)
            ->whereBetween('createtime', [$startOfYesterday, $endOfYesterday])
            ->sum('fee');

        // 计算同比增加的百分比
        $incomeIncreasePercentage = $yesterdayIncome > 0 ? (($todayIncome - $yesterdayIncome) / $yesterdayIncome) * 100 : 0;
        $expenseIncreasePercentage = $yesterdayExpense > 0 ? (($todayExpense - $yesterdayExpense) / $yesterdayExpense) * 100 : 0;

        return [
            'incomeIncreasePercentage' => round($incomeIncreasePercentage, 2),
            'expenseIncreasePercentage' => round($expenseIncreasePercentage, 2),
        ];
    }

}
