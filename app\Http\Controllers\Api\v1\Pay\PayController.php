<?php
namespace App\Http\Controllers\Api\v1\Pay;

use App\Events\ProfileBusinessProcess;
use App\Exceptions\AuthException;
use App\Http\Controllers\Controller;
use App\Models\IcbcOrderModel;
use App\Models\ProjectServiceDataModel;
use App\Services\ICBC\ICBCService;
use App\Services\ProfileBusinessProcessServices;
use App\Services\ProjectServiceDataServices;
use Illuminate\Http\Request;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PayController extends Controller
{
    const  IS_TEST = 1;
    private $projectServiceDataServices;
    private $profileBusinessProcessServices;
    public function __construct(ProjectServiceDataServices $projectServiceDataServices, ProfileBusinessProcessServices $profileBusinessProcessServices){
        $this->projectServiceDataServices = $projectServiceDataServices;
        $this->profileBusinessProcessServices = $profileBusinessProcessServices;
    }


    public function createToken($id)
    {
        $payload = [
            'exp' => time() + 3600,
            'action' => ACTION_PROFILE_REGISTER,
            'user_id' => $id,
        ];
        $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');

        return responseSuccess($token);
    }

    /**
     * 验证生成参数 预留其他支付方式
     * @param Request $request
     * @return array
     */
    private function validateParams(Request $request)
    {
        $validatedData = $request->validate([
            'token' => 'nullable|string',
            'project_code' => 'required|string',
            'is_test' => 'nullable|integer',
        ], [
            'project_code.required' => '业务代码不能为空',
            'project_code.string' => '业务代码不正确',
        ]);
        $validatedData['is_test'] = $validatedData['is_test'] ?? self::IS_TEST;
        return $validatedData;
    }

    /**
     * 生成注册支付二维码
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getQRCode(Request $request)
    {
        try {
            // 1. 参数验证
            $param = $this->validateParams($request);

            // 2. 获取用户信息
            if (!empty($param['token'])) {
                $decodeToken = JWT::decode($param['token'], new Key(env('JWT_KEY'), 'HS256'));
                if (!isset($decodeToken->action)) {
                    throw new \Exception('action not exist');
                }
                if ($decodeToken->action != ACTION_PROFILE_REGISTER) {
                    throw new \Exception('action error');
                }
                $userId = $decodeToken->user_id;
            }else{
                $user = $request->attributes->get('user');
                if( empty($user) ) {
                    throw new \Exception('用户信息过期');
                }
                $userId = $user->profileID;
            }
            // 3. 检查业务信息
            $payStatus = false;
            $projectInfo = $this->checkProjectInfo($param['project_code']);
            // 4. 检查业务进程
            $process = $this->checkBusinessProcess($userId, $param['project_code']);
            $process = $process->toArray();
            $process['merge_project_list'] = $projectInfo['merge_project'];
            // 5. 检查是否存在未支付订单  不查询旧单 重新生成 = 改为 下单回溯 下单对应进程
            $qrcode = $this->checkExistingOrder($process,$payStatus);

            if(!$payStatus){
                // 6. 计算支付金额
                $total = $this->calculatePaymentAmount($projectInfo, $param['is_test']);

                // 7. 生成支付二维码
                $qrcode = $this->generatePaymentQRCode($userId, $total, $projectInfo, $process);
            }
            return responseSuccess(compact('payStatus','qrcode', 'process'));

        } catch (\Exception $e) {
            Log::error('Generate QR code failed: ' . $e->getMessage());
            return responseFail($e->getMessage());
        }
    }

    /**
     * 检查业务信息
     * @param string $projectCode
     * @return array
     */
    private function checkProjectInfo(string $projectCode)
    {
        $projectInfo = $this->projectServiceDataServices->getInfoByCode($projectCode);
        $projectInfo = $projectInfo->toArray();
        
        if (empty($projectInfo) || $projectInfo['status'] == ProjectServiceDataModel::STATUS_CLOSE) {
            throw new \Exception('业务不存在');
        }
        
        if (empty($projectInfo['currency_info'])) {
            throw new \Exception('业务状态异常');
        }
        if( empty($projectInfo['payment_main']) ) {
            throw new \Exception('未定义收款主体');
        }
        //捆绑业务
        $projectInfo['merge_project'] = empty($projectInfo['merge_project_id']) ? [] : $this->projectServiceDataServices->getProjectServiceData(explode(",", $projectInfo['merge_project_id']));
        return $projectInfo;
    }

    /**
     * 检查业务进程
     * @param int $userId
     * @param string $projectCode
     * @return array
     */
    private function checkBusinessProcess(int $userId, string $projectCode)
    {
        $process = $this->profileBusinessProcessServices->getByProfileID($userId, $projectCode);
        if (empty($process)) {
            throw new \Exception('没有进行此业务');
        }
        return $process;
    }

    /**
     * 检查是否存在未支付订单
     * @param array $process
     * @param int|null $isTest
     * @return string|null
     */
    private function checkExistingOrder(array $process,&$payStatus)
    {
        if (!empty($process['pay_id'])) {
            $icbcOrders = IcbcOrderModel::find($process['pay_id']);
            if (!empty($icbcOrders) && $icbcOrders['pay_status'] == 0 ) {
                //检查订单有效性
                //银行接口原因 获取状态查询得多 支付回调会失效
//                $res = ICBCService::payStatusQRcode($icbcOrders['out_trade_no']);
//                if( $res !== false && $res['pay_status'] != 2 ){
//                    $payStatus = true;
                    return $this->tobase64($icbcOrders['qrcode']);
//                }
            }
        }
        return null;
    }

    /**
     * 计算支付金额
     * @param array $projectInfo
     * @param int|null $isTest
     * @return int
     */
    private function calculatePaymentAmount(array $projectInfo, ?int $isTest)
    {
        if (!empty($isTest)) {
            return env('WECHAT_FEE') * 100;
        }
        return bcmul(bcmul($projectInfo['price'], $projectInfo['currency_info']['rate'], 2), 100, 0);
    }

    /**
     * 生成支付二维码
     * @param int $userId
     * @param int $total
     * @param array $projectInfo
     * @param array $process
     * @return \Illuminate\Http\JsonResponse
     */
    private function generatePaymentQRCode(int $userId, int $total, array $projectInfo, array $process)
    {
        DB::beginTransaction();
        try {
            $outTradeNo = ICBCService::generateOutTradeNo($userId);
            $notify = route('payNotify');
            $result = ICBCService::generatePayQRcode($outTradeNo, $total, $notify);
            if (!$result) {
                throw new \Exception('生成支付二维码失败');
            }
            // 创建订单
            $order = IcbcOrderModel::query()->create([
                'mer_id' => $projectInfo['payment_main']['icbc_mer_id'],
                'app_id' => $projectInfo['payment_main']['icbc_app_id'],
                'user_id' => $userId,
                'qrcode_msg_id' => $result['msg_id'],
                'out_trade_no' => $outTradeNo,
                'qrcode' => $result['qrcode'],
                'total_amt' => $total,
                'attach' => $result['attach'],
                'remark' => $process['project_remark'],
                'process_id' => $process['id'],
                'project_id' => $projectInfo['id'],
                'merge_project_id' => $projectInfo['merge_project_id'],
                'currency' => $projectInfo['currency_info']['currency'],
                'currency_code' => $projectInfo['currency'],
                'currency_rate' => $projectInfo['currency_info']['rate'],
            ]);

            if (!$order) {
                throw new \Exception('GENERATE_ORDER_ERROR');
            }

            // 更新业务进程
            $res = $this->profileBusinessProcessServices->update($process['id'], ['pay_id' => $order->id]);
            if (!$res) {
                throw new \Exception('PROCESS UPDATE FAIL');
            }

            DB::commit();
            return $this->tobase64($result['qrcode']);

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function tobase64($qrcodeUrl){
        $img = \SimpleSoftwareIO\QrCode\Facades\QrCode::format('png')->size(150)->generate($qrcodeUrl);
        return 'data:image/png;base64,' . base64_encode($img);
    }


    /**
     * 回调处理
     * @return \Illuminate\Http\JsonResponse|void
     */
    public function notify(Request $request)
    {
        $queryString = file_get_contents("php://input");
        debugLog("记录回调：".json_encode($queryString));
        // 解码查询字符串
        $decodedString = urldecode($queryString);

        // 将查询字符串解析为数组
        parse_str($decodedString, $data);

        $response_biz_content = isset($data['biz_content'])?json_decode($data['biz_content'], true):'';

        if ($response_biz_content && $response_biz_content['return_code'] == 0) {

            try {
                $order = IcbcOrderModel::query()->where('pay_status', 0)->where('out_trade_no', $response_biz_content['out_trade_no'])->first();

                if ($order) {
                    $order->pay_msg_id = $response_biz_content['msg_id'];
                    $order->order_id   = $response_biz_content['order_id'];
                    $order->pay_time   = $response_biz_content['pay_time'];
                    $order->pay_status = 1;
                    $order->cust_id    = $response_biz_content['cust_id'];
                    $order->card_no    = $response_biz_content['card_no'];
                    $order->bank_name  = isset($response_biz_content['bank_name'])??$response_biz_content['bank_name'];
                    $order->channel    = $response_biz_content['channel'];
                    $order->attach     = $response_biz_content['attach'];
                    $order->tp_cust_id = isset($response_biz_content['tp_cust_id'])?? $response_biz_content['tp_cust_id'];
                    $order->trx_ser_no = isset($response_biz_content['trx_ser_no'])??$response_biz_content['trx_ser_no'];
                    $order->tp_order_id= isset($response_biz_content['tp_order_id'])??$response_biz_content['tp_order_id'];
                    $order->sub_open_id= isset($response_biz_content['sub_open_id'])??$response_biz_content['sub_open_id'];
                    $order->bank_type  = isset($response_biz_content['bank_type'])??$response_biz_content['bank_type'];
                    $order->tp_user_id = isset($response_biz_content['tp_user_id'])??$response_biz_content['tp_user_id'];
                    $order->save();

                    event(new ProfileBusinessProcess($order->id,$response_biz_content['total_amt']));

                }
            } catch (\Exception $exception) {
                DB::rollBack();
                debugLog("回调报错1：".json_encode($exception->getTrace()));
                Log::info($exception->getTrace());
                return response()->json(['code' => 1, 'msg' => 'pay fail']);
            }
        } else {
            return response()->json(['code' => 1, 'msg' => 'not response data']);
        }
    }

    /**
     * 检查支付订单状态
     * @return void
     */
    public function checkRegisterOrder(Request $request)
    {
        $profileId = $request->get('profileID');
        if (!$profileId || !is_numeric($profileId) || $profileId < 0) {
            return responseFail();
        }
        IcbcOrderModel::checkRegisterOrder($profileId);
        return responseSuccess();

    }

}