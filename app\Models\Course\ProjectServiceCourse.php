<?php

namespace App\Models\Course;

use App\Models\ProfileInfoModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectServiceCourse extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'cna_project_service_course';
    protected $fillable = [
        'id',
        'name',
        'type',
        'url',
        'service_data_id',
        'sort'
    ];

    // 定义课程和用户的多对多关系
    public function profileCourse()
    {
        return $this->belongsToMany(
            ProfileInfoModel::class,
            'cna_profile_course',
            'project_service_course_id',
            'profile_id',
            'id',
            'profileID'
        )
            ->withPivot('progress') // 指定中间表的额外字段
            ;
    }




    //时间转换
    protected function serializeDate(\DateTimeInterface $date){
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }
}