<?php

namespace App\Rules\User;

use App\Models\CountryModel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * 银行卡号验证规则类
 * 
 * 该类用于验证银行卡号的格式和有效性，包含以下验证：
 * 1. 格式验证：检查银行卡号是否只包含数字
 * 2. 长度验证：检查银行卡号长度是否在13-19位之间
 * 3. <PERSON>hn算法验证：使用Luhn算法验证银行卡号的有效性
 */
class CheckBankCardRule implements ValidationRule
{
    /**
     * 执行验证规则
     *
     * @param string $attribute 验证的属性名
     * @param mixed $value 验证的值
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail 验证失败时的回调函数
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // 只在中国环境下进行银行卡验证
        if (env('COUNTRY') == CountryModel::COUNTRY_ID_CHINA) {
            // 移除银行卡号中的空格和连字符
            $cardNumber = preg_replace('/\s+/', '', $value);
            
            // 验证银行卡号格式：检查是否只包含数字
            if (!preg_match('/^\d+$/', $cardNumber)) {
                $fail(__('bank card number format error'));
                return;
            }
            
            // 验证银行卡号长度：检查长度是否在13-19位之间
            // 中国银行卡通常是16位，但国际标准允许13-19位
            if (strlen($cardNumber) < 13 || strlen($cardNumber) > 19) {
                $fail(__('bank card number length error'));
                return;
            }
            
            // 使用Luhn算法验证银行卡号的有效性
            if (!$this->luhnCheck($cardNumber)) {
                $fail(__('bank card number invalid'));
                return;
            }
        }
    }
    
    /**
     * Luhn算法验证银行卡号
     * 
     * Luhn算法（也称为"模10"算法）是一种简单的校验和算法，
     * 用于验证各种识别号码，包括信用卡号码、IMEI号码等。
     * 
     * 算法步骤：
     * 1. 从右到左，每隔一位数字乘以2
     * 2. 如果乘积大于9，则减去9
     * 3. 将所有数字相加
     * 4. 如果总和能被10整除，则号码有效
     *
     * @param string $cardNumber 银行卡号
     * @return bool 验证结果，true表示有效，false表示无效
     */
    private function luhnCheck(string $cardNumber): bool
    {
        $sum = 0;
        $length = strlen($cardNumber);
        $parity = $length % 2; // 确定从哪一位开始乘以2
        
        for ($i = 0; $i < $length; $i++) {
            $digit = intval($cardNumber[$i]);
            
            // 根据卡号长度的奇偶性，决定从哪一位开始乘以2
            if ($i % 2 == $parity) {
                $digit *= 2;
                // 如果乘积大于9，则减去9（等同于将各位数字相加）
                if ($digit > 9) {
                    $digit -= 9;
                }
            }
            
            $sum += $digit;
        }
        
        // 如果总和能被10整除，则银行卡号有效
        return ($sum % 10) == 0;
    }
} 