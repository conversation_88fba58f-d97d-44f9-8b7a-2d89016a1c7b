<?php

namespace App\Services;

use App\Models\NotificationInfoModel;
use App\Models\NotificationTemplateModel;
use App\Models\ProfileInfoModel;
use App\Exceptions\AuthException;
use Illuminate\Support\Facades\Log;

class NotificationServices
{
    protected $model;
    protected $templateModel;
    
    // 缓存字段列表
    private static $templateColumns = null;

    // 模板变量匹配模式
    const TEMPLATE_PATTERN = '/<<\{\{([^}]+)\}\}>>/';
    
    // 支持的多语言配置
    const SUPPORTED_LANGUAGES = ['EN', 'MS', 'ZH', 'ZT'];
    
    // 通知模板字段类型配置
    const FIELD_TYPES = [
        'notificationTitle',      // 通知标题字段
        'notificationDescription' // 通知描述字段
    ];

    public function __construct(
        NotificationInfoModel $model = null,
        NotificationTemplateModel $templateModel = null
    ) {
        $this->model = $model ?? app(NotificationInfoModel::class);
        $this->templateModel = $templateModel ?? app(NotificationTemplateModel::class);
    }

    /**
     * 获取通知列表
     * @param array $params 查询参数
     * @return array
     */
    public function getList($params = [])
    {
        $query = $this->model->where('notificationTarget', $params['profileID'] ?? 0)
            ->when(!empty($params['keyword']), function ($query) use ($params) {
                $keyword = trim($params['keyword']);
                $query->where(function ($query) use ($keyword) {
                    // 多语言标题搜索
                    foreach (self::SUPPORTED_LANGUAGES as $lang) {
                        $query->orWhere("notificationTitle{$lang}", 'like', "%{$keyword}%");
                    }
                    
                    // 多语言描述搜索
                    foreach (self::SUPPORTED_LANGUAGES as $lang) {
                        $query->orWhere("notificationDescription{$lang}", 'like', "%{$keyword}%");
                    }
                    
                    // 部门搜索
                    $query->orWhere('notificationDepartment', 'like', "%{$keyword}%");
                    
                    // 根据用户名搜索
                    $profileIds = ProfileInfoModel::where('profileName', 'like', "%{$keyword}%")
                                                ->pluck('profileID')
                                                ->toArray();
                    if (!empty($profileIds)) {
                        $query->orWhereIn('notificationTarget', $profileIds);
                    }
                });
            })
            ->when(!empty($params['department']), function ($query) use ($params) {
                $query->where('notificationDepartment', $params['department']);
            })
            ->when(!empty($params['start_date']), function ($query) use ($params) {
                $query->where('createTime', '>=', $params['start_date']);
            })
            ->when(!empty($params['end_date']), function ($query) use ($params) {
                $query->where('createTime', '<=', $params['end_date'] . ' 23:59:59');
            })
            ->orderBy('createTime', 'desc');
        
        // 分页
        $pageSize = max(1, min(100, intval($params['page_size'] ?? 10)));
        $list = $query->paginate($pageSize);
        
        // 处理结果数据
        $items = collect($list->items())->map(function ($item) {
            // 添加部门名称
            $item->departmentName = $item->notificationDepartment 
                ? NotificationInfoModel::getNotificationDepartment($item->notificationDepartment) 
                : null;
            
            // 添加创建用户信息
            if ($item->createUser) {
                $item->createUserName = ProfileInfoModel::find($item->createUser)->profileName ?? null;
            }
            
            return $item;
        });
        
        // 构建分页信息
        $paginate = [
            'page_size' => $pageSize,
            'current_page' => $list->currentPage(),
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
            'has_more' => $list->hasMorePages(),
        ];
        
        return compact('items', 'paginate');
    }

    public function info($id)
    {
        $info = $this->model->find($id);
        if (empty($info)) {
            throw new AuthException('信息不存在');
        }
        // 获取上一条和下一条
        $prevID = $this->model->where('notificationID', '<', $info->notificationID)
            ->where('notificationTarget',$info->notificationTarget)
            ->orderBy('createTime', 'desc')
            ->first(['notificationID']);

        $nextID = $this->model->where('notificationID', '>', $info->notificationID)
            ->where('notificationTarget',$info->notificationTarget)
            ->orderBy('createTime', 'asc')
            ->first(['notificationID']);
        $paginate = [
            'prev_id' => $prevID ? $prevID->notificationID : null,
            'next_id' => $nextID ? $nextID->notificationID : null
        ];
        return compact('info', 'paginate');
    }

    public function save($data, $id = null)
    {
        // 验证必要字段
        if (empty($data['notificationTarget'])) {
            throw new \Exception('通知目标用户不能为空');
        }

        // 设置创建时间
        if (empty($id)) {
            $data['createTime'] = now();
        }

        if ($id) {
            // 更新现有记录
            $notification = $this->model->find($id);
            if (!$notification) {
                throw new \Exception('通知记录不存在');
            }
            
            $result = $notification->update($data);
            if (!$result) {
                throw new \Exception('更新通知失败');
            }
            
            return $notification;
        } else {
            // 创建新记录
            $result = $this->model->create($data);
            if (!$result) {
                throw new \Exception('创建通知失败');
            }
            
            return $result;
        }
    }

    /**
     * 发送通知（异步队列方式）
     * @param string $templateCode 模板代码
     * @param array $sendData 发送数据
     * @param array $sendUser 发送用户
     * @return bool
     */
    public function send($templateCode, $sendData, $sendUser)
    {
        //校验必要参数
        if( empty($templateCode) || empty($sendData['targetUser']) || empty($sendUser['profileID']) ){
            return false;
        }
        \App\Jobs\SendNotice::dispatch($sendData, $templateCode, $sendUser)->onQueue('SendNoticeJob');
        return true;
    }

    /**
     * 发送通知（同步方式）
     * @param array $data 通知数据，包含模板变量和目标用户信息
     * @param string $code 通知类型代码，对应模板表中的eventCode
     * @param array $user 操作用户信息，包含profileID和profileRole
     * @return bool
     */
    public function sendNotification($data, $code, $user = []): bool
    {
        try {
            if (!$this->validateInputs($data, $code)) {
                throw new \Exception('Invalid inputs');
            }

            $template = $this->templateModel->where('eventCode', $code)->first();
            if (!$template) {
                throw new \Exception('Template not found');
            }

            // 构建通知数据
            $notificationData = [
                'notificationDepartment' => 0,
                'notificationTarget' => $data['targetUser'],
                'createUser' => $user['profileID'] ?? 0,
                'createRole' => $user['profileRole'] ?? 0,
            ];

            // 处理多语言模板内容
            foreach (self::getTemplateColumns() as $column) {
                if (isset($template[$column])) {
                    $notificationData[$column] = $this->replaceTemplateVariables($template[$column], $data);
                }
            }

            // 使用 save 方法创建通知
            $result = $this->save($notificationData);
            
            if (!$result) {
                throw new \Exception('Failed to create notification');
            }

            return true;

        } catch (\Exception $e) {
            Log::error("NotificationServices: {$e->getMessage()}", ['code' => $code]);
            return false;
        }
    }

    /**
     * 验证输入数据
     * @param array $data 通知数据
     * @param string $type 通知类型
     * @return bool
     */
    private function validateInputs($data, $code): bool
    {
        return !empty($code) && 
               !empty($data['targetUser']) && 
               is_array($data);
    }

    /**
     * 动态生成多语言模板字段列表
     * 根据FIELD_TYPES和SUPPORTED_LANGUAGES自动生成所有字段组合
     * @return array 包含所有多语言字段名的数组
     */
    private static function getTemplateColumns(): array
    {
        if (self::$templateColumns === null) {
            self::$templateColumns = [];
            foreach (self::FIELD_TYPES as $fieldType) {
                foreach (self::SUPPORTED_LANGUAGES as $lang) {
                    self::$templateColumns[] = $fieldType . $lang;
                }
            }
        }
        return self::$templateColumns;
    }

    /**
     * 替换模板中的变量占位符
     * @param string $template 包含变量的模板字符串
     * @param array $data 用于替换变量的数据数组
     * @return string 替换后的模板内容
     */
    private function replaceTemplateVariables($template, $data)
    {
        if (empty($template)) return $template;

        return preg_replace_callback(
            self::TEMPLATE_PATTERN,
            function($matches) use ($data) {
                $key = $matches[1];
                return isset($data[$key]) ? $data[$key] : $matches[0];
            },
            $template
        );
    }

    /**
     * 获取支持的语言列表
     * @return array
     */
    public static function getSupportedLanguages(): array
    {
        return self::SUPPORTED_LANGUAGES;
    }

    /**
     * 获取字段类型列表
     * @return array
     */
    public static function getFieldTypes(): array
    {
        return self::FIELD_TYPES;
    }
}