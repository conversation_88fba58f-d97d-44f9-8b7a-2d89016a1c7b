<?php

namespace App\Http\Controllers\Api\v1\User;

use App\Events\UpdateUserAvatar;
use App\Events\UpdateUserEmail;
use App\Exceptions\DefaultException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Register\CommitVerifyEmail;
use App\Http\Requests\Register\CommitVerifySms;
use App\Http\Requests\Register\VerifyEmail;
use App\Http\Requests\Register\VerifyPhone;
use App\Http\Requests\User\UpdateUserInfo;
use App\Models\ActiveLog;
use App\Models\BankCardCn;
use App\Models\BankModel;
use App\Models\BusinessCase;
use App\Models\CountryModel;
use App\Models\Firm;
use App\Models\FirmRelation;
use App\Models\Nric;
use App\Models\PassportCn;
use App\Models\ProfessionalInfoModel;
use App\Models\ProfessionalUserDataModel;
use App\Models\Qualification;
use App\Services\SmsService;
use Illuminate\Http\Request;
use App\Models\ProfileInfoModel;
use App\Models\Setting;
use App\Models\SkillInfoModel;
use App\Models\SkillUserDataModel;
use App\Models\TeamRank;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Services\EmailService;
use App\Services\OcrService;
use Illuminate\Support\Facades\Validator;
use App\Rules\User\CheckNricRule;
use App\Rules\User\CheckNricValidPeriodRule;
use App\Rules\User\CheckBankCardRule;
use App\Services\OssService;
use App\Services\SignatureService;

class UserController extends Controller
{
    private $profileInfoModel;
    private $request;
    private $emailService;
    private $smsService;

    public function __construct(
        ProfileInfoModel $profileInfoModel,
        Request          $request,
        EmailService     $emailService,
        SmsService       $smsService
    ) {
        $this->profileInfoModel = $profileInfoModel;
        $this->request = $request;
        $this->emailService = $emailService;
        $this->smsService = $smsService;
    }

    public function profile(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->validate([
            'profileID' => ['integer', 'min:1'],
        ]);
        $user = $this->profileInfo($body['profileID'] ?? 0, $user);

        return responseSuccess($user);
    }

    public function cardProfile(Request $request)
    {
        $body = $request->validate([
            'profileID' => ['required', 'integer', 'min:1'],
        ]);
        $user = $this->profileInfo($body['profileID']);
        unset($user['profileNRIC'], $user['profileAddressUnit'], $user['profileAddressStreet'], $user['bank_account'], $user['bank_branch']);

        return responseSuccess($user);
    }

    //个人简介
    private function profileInfo($profileId = 0, $user = [])
    {
        if ($profileId) {
            $user = ProfileInfoModel::with(['district:id,adname', 'profession:id,nameZH'])->where('profileID', $profileId)->first();
        }
        if (empty($user)) {
            return [];
        }
        $user['userProfessional'] = $this->profileInfoModel->find($user['profileID'])->professional->select('professionalID', 'professionalDescription');
        // $user['userExperience'] = $this->profileInfoModel->find($user['profileID'])->experience->select('experienceID', 'experienceDescription');
        $user['userSkill'] = $this->profileInfoModel->find($user['profileID'])->skill->select('skillID', 'skillDescription');
        $user['profileAvatar'] = storageUrl($user['profileAvatar'] ?: ProfileInfoModel::AVATAR_PATH);
        $user['nationality'] = $this->profileInfoModel->find($user['profileID'])->nationality()->first();
        $user['settingLanguageAbility'] = $user['settingLanguageAbility'] ? explode(',', $user['settingLanguageAbility']) : [];
        $setting = Setting::where('profile_id', $user['profileID'])->first();
        if ($setting) {
            $user['settingLanguage'] = $setting['language'];
            $user['settingTimezone'] = $setting['timezone'];
            $user['settingDateFormat'] = $setting['date_format'];
            $user['settingTimeFormat'] = $setting['time_format'];
            $user['settingCurrency'] = $setting['currency'];
            $user['settingNotifyType'] = $setting['notify_type'];
            $user['settingNotifyEmergency'] = $setting['notify_emergency'];
            $user['settingNotifySuspiciousOperation'] = $setting['notify_suspicious_operation'];
            $user['settingNotifySafeUpdated'] = $setting['notify_safe_updated'];
            $user['settingNotifyRecPrivateMsg'] = $setting['notify_rec_private_msg'];
            $user['settingNotifyImportanceUpdate'] = $setting['notify_importance_update'];
            $user['settingNotifySystemUpdate'] = $setting['notify_system_update'];
            $user['settingNotifyJoinInvestigate'] = $setting['notify_join_investigate'];
            $user['settingNotifyBill'] = $setting['notify_bill'];
        }
        //联号事务所
        $firm = FirmRelation::where('profile_id', $user['profileID'])->first();
        if ($firm) {
            $firm['code'] = Firm::where('profile_id', $firm['pre_id'] ?: $user['profileID'])->value('code');
        }
        $user['firm'] = $firm;
        //职务
        if ($user['team_group'] && $user['team_rank']) {
            $user['job'] = TeamRank::where('id', $user['team_rank'])->value('job');
        } else {
            $nextUserCount = ProfileInfoModel::where('pre_id', $user['profileID'])->count();
            if ($nextUserCount) {
                $user['job'] = '管理合伙人';
            } else {
                $user['job'] = '联盟合伙人';
            }
        }
        return $user;
    }

    /**
     * Method 修改用户简介
     *
     * @param UpdateUserInfo $request [explicite description]
     *
     * @return void
     */
    public function updateProfile(UpdateUserInfo $request)
    {
        $user = $request->attributes->get('user');
        $profileID = $user['profileID'];
        $body = $request->all();
        $profileAddressStreet = $body['profileAddressStreet'];
        $profileAddressUnit = $body['profileAddressUnit'];
        $profileAddressCountry = $body['profileAddressCountry'];
        $profileAddressStateId = $body['profileAddressStateId'];
        $profileAddressCityId = $body['profileAddressCityId'];
        $profileAddressDistrictId = $body['profileAddressDistrictId'];
        $professionId = $body['profession_id'] ?? 0;
        $professional = $body['professional'] ?? [];
        $edu = $body['edu'] ?? '';
        $eduSubject = $body['eduSubject'] ?? '';
        $eduDatetime = $body['eduDatetime'] ?? '';
        // $experience = $body['experience'];
        $skill = $body['skill'] ?? [];
        $settingLanguageAbility = $body['settingLanguageAbility'] ?? ''; // 语言能力

        // if (!is_array($professional) || !is_array($experience) || !is_array($skill)) {
        if (!is_array($professional) || !is_array($skill)) {
            return responseFail();
        }

        $professionalInfoModel = new ProfessionalInfoModel;
        $professionalUserDataModel = new ProfessionalUserDataModel;
        // $experienceInfoModel = new ExperienceInfoModel;
        // $experienceUserDataModel = new ExperienceUserDataModel;
        $skillInfoModel = new SkillInfoModel;
        $skillUserDataModel = new SkillUserDataModel;

        $professionalModelData = $professionalInfoModel->getData()->pluck('professionalID')->toArray();
        // $experienceModelData = $experienceInfoModel->getData()->pluck('experienceID')->toArray();
        $skillModelData = $skillInfoModel->getData()->pluck('skillID')->toArray();

        try {

            DB::beginTransaction();

            //校验个人专业资格
            foreach ($professional as $k => $v) {
                if (!isset($v['professionalID']) || !isset($v['professionalDescription']) || !$v['professionalID'] || !$v['professionalDescription']) {
                    throw new DefaultException(__('please choose a professional qualification'));
                }
                if (!in_array($v['professionalID'], $professionalModelData)) {
                    throw new DefaultException(__('please choose a professional qualification'));
                }
                $professional[$k]['professionalProfileID'] = $profileID;
                $professional[$k]['created_at'] = Carbon::now();
                $professional[$k]['updated_at'] = Carbon::now();
            }
            $professionalUserDataModel->where('professionalProfileID', $profileID)->delete();
            if ($professional) {
                $professionalUserDataModel->insert($professional);
            }

            //检验个人经验
            // foreach ($experience as $k => $v) {
            //     if (!isset($v['experienceID']) || !isset($v['experienceDescription']) || !$v['experienceID'] || !$v['experienceDescription']) {
            //         throw new DefaultException('请选择个人经验');
            //     }
            //     if (!in_array($v['experienceID'], $experienceModelData)) {
            //         throw new DefaultException('请选择个人经验');
            //     }
            //     $experience[$k]['experienceProfileID'] = $profileID;
            //     $experience[$k]['created_at'] = Carbon::now();
            //     $experience[$k]['updated_at'] = Carbon::now();
            // }
            // $experienceUserDataModel->where('experienceProfileID', $profileID)->delete();
            // $experienceUserDataModel->insert($experience);

            //检验个人技能
            foreach ($skill as $k => $v) {
                if (!isset($v['skillID']) || !isset($v['skillDescription']) || !$v['skillID'] || !$v['skillDescription']) {
                    throw new DefaultException(__('please select personal skills'));
                }
                if (!in_array($v['skillID'], $skillModelData)) {
                    throw new DefaultException(__('please select personal skills'));
                }
                $skill[$k]['skillProfileID'] = $profileID;
                $skill[$k]['created_at'] = Carbon::now();
                $skill[$k]['updated_at'] = Carbon::now();
            }
            $skillUserDataModel->where('skillProfileID', $profileID)->delete();
            if ($skill) {
                $skillUserDataModel->insert($skill);
            }

            //修改个人信息
            $updateData = [
                'profileAddressStreet' => $profileAddressStreet,
                'profileAddressUnit' => $profileAddressUnit,
                'profileAddressCountry' => $profileAddressCountry,
                'profileAddressStateId' => $profileAddressStateId,
                'profileAddressCityId' => $profileAddressCityId,
                'profileAddressDistrictId' => $profileAddressDistrictId,
                'settingLanguageAbility' => $settingLanguageAbility,
                'profession_id' => $professionId
            ];

            if (isset($body['profileDesc'])) {
                $updateData['profileDesc'] = $body['profileDesc'];
            }

            if (isset($body['edu'])) {
                $updateData['edu'] = $body['edu'];
            }

            if (isset($body['eduSubject'])) {
                $updateData['eduSubject'] = $body['eduSubject'];
            }

            if (isset($body['eduDatetime'])) {
                $updateData['eduDatetime'] = $body['eduDatetime'];
            }

            $this->profileInfoModel->where('profileID', $profileID)->update($updateData);
            //记录活动日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ACTIVE_UPDATE,
                ActiveLog::API_V1_USER_UPDATEPROFILE,
                $user['profileID'],
                $body
            );

            DB::commit();

            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail($e->getMessage());
        }
    }

    /**
     * Method 修改头像
     *
     * @return void
     */
    public function updateUserAvatar()
    {
        $user = $this->request->attributes->get('user');
        $file = $this->request->file('avatar');
        $allowExtension = ['jpg', 'jpeg', 'png'];
        $allowType = ['image/jpeg', 'image/png', 'image/jpg'];
        $allowSize = env('ALLOW_FILE_SIZE') * 1024 * 1024;

        if (!$file) {
            return responseFail(__('param error', ['param' => 'avatar']));
        }

        if (!in_array($file->getClientOriginalExtension(), $allowExtension)) {
            return responseFail(__('incorrect format img'));
        }

        if (!in_array($file->getClientMimeType(), $allowType)) {
            return responseFail(__('incorrect format img'));
        }

        if ($file->getSize() > $allowSize) {
            return responseFail(__('exceed size img', ['limit' => env('ALLOW_FILE_SIZE') . 'M']));
        }

        $oldImage = $user['profileAvatar'];
        //$resource = $file->store('images/avatar', 'public');
        //$resource = $file->storeAs('images/avatar', generateUploadFilename($file->getClientOriginalExtension()), 'public');
        $resourceRpath = 'images/avatar/' . generateUploadFilename();
        $resourceApath = Storage::disk('public')->path('') . $resourceRpath;
        $res = resizeImage($file, $resourceApath, 1500, IMAGETYPE_JPEG);
        if (!$res) {
            return responseFail(__('image upload failed'));
        }
        $update = ['profileAvatar' => $resourceRpath];
        $res = $this->profileInfoModel::where('profileID', $user['profileID'])->update($update);
        if ($res) {
            if ($oldImage) {
                Storage::disk('public')->delete($oldImage);
            }
            //记录活动日志
            ActiveLog::log(
                $user['profileID'],
                ActiveLog::ACTIVE_UPDATE,
                ActiveLog::API_V1_USER_UPDATEAVATAR,
                $user['profileID'],
                $update
            );
            event(new UpdateUserAvatar($user['profileID']));
            return responseSuccess(storageUrl($resourceRpath));
        } else {
            return responseFail();
        }
    }

    //修改邮箱发送验证码
    public function changeMailSendCode(VerifyEmail $request)
    {
        $body = $request->all();
        $nationalityID = $request->input('nationalityID', '');
        $language = getLanguageByNationalityID($nationalityID);
        $user = $request->attributes->get('user');

        return $this->emailService->sendVerificationCodeEmail(
            $body['email'],
            config('email.verification_code_scene.change_email'),
            $language,
            $user['profileID']
        );
    }

    //修改邮箱提交验证啊
    public function changeMailVerifyCode(CommitVerifyEmail $request)
    {
        $body = $request->all();
        $user = $request->attributes->get('user');
        $this->emailService->verifyEmail(
            $body['email'],
            $body['code'],
            config('email.verification_code_scene.change_email'),
            $user['profileID']
        );
        //更新客户资料
        $update = ['profileEmail' => $body['email']];
        ProfileInfoModel::where('profileID', $user['profileID'])->update($update);
        //记录活动日志
        ActiveLog::log(
            $user['profileID'],
            ActiveLog::ACTIVE_UPDATE,
            ActiveLog::API_V1_USER_CHANGEEMAILVERIFY,
            $user['profileID'],
            $update
        );
        event(new UpdateUserEmail($user['profileID']));

        return responseSuccess();
    }

    //修改手机号发送验证码
    public function changePhoneSendCode(VerifyPhone $request)
    {
        $body = $request->all();
        $phone = $body['phone'];
        $prefixID = $body['prefixID'];
        $mobilePrefixModel = new CountryModel;

        //整合区号+手机号
        $resetPhoneData = $mobilePrefixModel->resetPhone($prefixID, $phone);
        $resetPhone = $resetPhoneData['phone'];
        $resetCountryCode = $resetPhoneData['countryCode'];
        $user = $request->attributes->get('user');

        return $this->smsService->sendVerificationCodeSms(
            $resetPhone,
            config('sms.verification_code_scene.change_phone'),
            $resetCountryCode,
            $user['profileID']
        );
    }

    //修改手机号提交验证码
    public function changePhoneVerifyCode(CommitVerifySms $request)
    {
        $body = $request->all();
        $user = $request->attributes->get('user');
        $this->smsService->verifySms(
            $body['phone'],
            $body['code'],
            config('sms.verification_code_scene.change_phone'),
            $user['profileID']
        );
        //更新客户资料
        $update = ['profileContact' => $body['phone']];
        ProfileInfoModel::where('profileID', $user['profileID'])->update($update);
        //记录活动日志
        ActiveLog::log(
            $user['profileID'],
            ActiveLog::ACTIVE_UPDATE,
            ActiveLog::API_V1_USER_CHANGEPHONEVERIFY,
            $user['profileID'],
            $update
        );

        return responseSuccess();
    }

    public function uploadNric()
    {
        $user = $this->request->attributes->get('user');
        $file = $this->request->file('file');
        $type = $this->request->post('type');
        if (!in_array($type, [Nric::TYPE_FACE, Nric::TYPE_BACK])) {
            return responseFail(__('param error', ['param' => 'type']));
        }
        $actualType = $type;
        $allowExtension = ['jpg', 'jpeg', 'png', 'webp', 'bmp', 'tiff'];
        $allowType = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'image/bmp', 'image/tiff'];
        $allowSize = env('ALLOW_FILE_SIZE') * 1024 * 1024;
        if (!$file) {
            return responseFail(__('param error', ['param' => 'file']));
        }
        if (!in_array($file->getClientOriginalExtension(), $allowExtension)) {
            return responseFail(__('incorrect format img'));
        }
        if (!in_array($file->getClientMimeType(), $allowType)) {
            return responseFail(__('incorrect format img'));
        }
        if ($file->getSize() > $allowSize) {
            return responseFail(__('exceed size img', ['limit' => env('ALLOW_FILE_SIZE') . 'M']));
        }
        $filePath = $file->store(Nric::SAVE_PATH, 'public');
        if ($type == Nric::TYPE_FACE) {
            $update = ['face_file' => $filePath];
        } else if ($type == Nric::TYPE_BACK) {
            $update = ['back_file' => $filePath];
        }
        if (env('COUNTRY') == CountryModel::COUNTRY_ID_CHINA) {
            $ocrRes = OcrService::idCard($filePath);
            if (!$ocrRes) {
                return responseFail(__('ID card photo error'));
            }

            if (($type == Nric::TYPE_FACE && empty($ocrRes['id_number']))
                || ($type == Nric::TYPE_BACK && empty($ocrRes['valid_period']))
            ) {
                return responseFail(__('ID card photo error'));
            }
            $rules = [
                'id_number' => [new CheckNricRule],
                'valid_period' => [new CheckNricValidPeriodRule],
            ];
            $messages = [
                'id_number' => __('ID card photo error'),
            ];
            $validator = Validator::make($ocrRes, $rules, $messages);
            if ($validator->fails()) {
                $errors = $validator->errors()->all();
                return responseFail($errors[0]);
            }
            if (!empty($ocrRes['id_number'])) {
                $actualType = Nric::TYPE_FACE;
                //更新身份证号码
                ProfileInfoModel::where('profileID', $user['profileID'])->update(['profileNRIC' => $ocrRes['id_number']]);
            } else {
                $period = explode('-', $ocrRes['valid_period']);
                $period[1] = trim($period[1]);
                if ($period[1] != '长期') {
                    $ocrRes['expire_time'] = strtotime(str_replace('.', '-', $period[1]));
                }
                $actualType = Nric::TYPE_BACK;
            }
            $update = $ocrRes;
        }
        $record = Nric::where('profile_id', $user['profileID'])->first();

        // 匹配身份证上的姓名和注册的姓名是否一致
        if (isset($update['name']) && $update['name'] != $user['profileName']) {
            $update['status'] = Nric::STATUS_SUSPICIOUS;
        }

        if ($record) {
            Nric::where('profile_id', $user['profileID'])->update($update);
        } else {
            $update['profile_id'] = $user['profileID'];
            Nric::create($update);
        }
        //更新注册时的服务合同
        if (isset($update['face_file'])) {
            (new SignatureService)->contractRedo($user['profileID']);
        }

        // 如果识别数据有名字, 返回给展示在填写银行卡页面
        $returnContent = ['actual_type' => $actualType];
        if (isset($ocrRes['name'])) {
            $returnContent['name'] = $ocrRes['name'];
        }

        return responseSuccess($returnContent);
    }

    public function registerSetting()
    {
        $user = $this->request->attributes->get('user');
        $res = ProfileInfoModel::initialRes($user);

        return responseSuccess(['register_setting' => $res]);
    }

    //上传中国护照
    public function uploadPassportCn()
    {
        if (env('COUNTRY') != CountryModel::COUNTRY_ID_CHINA) {
            return responseFail();
        }
        $user = $this->request->attributes->get('user');
        $file = $this->request->file('file');
        $allowExtension = ['jpg', 'jpeg', 'png', 'webp', 'bmp', 'tiff'];
        $allowType = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'image/bmp', 'image/tiff'];
        $allowSize = env('ALLOW_FILE_SIZE') * 1024 * 1024;
        if (!$file) {
            return responseFail(__('param error', ['param' => 'file']));
        }
        if (!in_array($file->getClientOriginalExtension(), $allowExtension)) {
            return responseFail(__('incorrect format img'));
        }
        if (!in_array($file->getClientMimeType(), $allowType)) {
            return responseFail(__('incorrect format img'));
        }
        if ($file->getSize() > $allowSize) {
            return responseFail(__('exceed size img', ['limit' => env('ALLOW_FILE_SIZE') . 'M']));
        }
        $filePath = $file->store(PassportCn::SAVE_PATH, 'public');
        $ocrRes = OcrService::passportCn($filePath);
        if (!$ocrRes) {
            return responseFail(__('passport photo error'));
        }
        $validateKey = ['passport_number', 'name', 'valid_to_date'];
        foreach ($validateKey as $k) {
            if (empty($ocrRes[$k])) {
                return responseFail(__('passport photo error'));
            }
        }
        $validDatePattern = '/^(20)\d{2}.((0[1-9])|(10|11|12)).(([0-2][1-9])|10|20|30|31)$/';
        if (!preg_match($validDatePattern, $ocrRes['valid_to_date'])) {
            return responseFail(__('incorrect format of passport expiration date'));
        }
        $now = strtotime('today');
        $validTime = strtotime(str_replace('.', '-', $ocrRes['valid_to_date']));
        if ($validTime <= $now) {
            return responseFail(__('passport has expired'));
        }
        $ocrRes['expire_time'] = $validTime;
        $ocrRes['file'] = $filePath;
        // 匹配身份证上的姓名和注册的姓名是否一致
        if ($ocrRes['name'] != $user['profileName']) {
            $ocrRes['status'] = PassportCn::STATUS_SUSPICIOUS;
        }
        $record = PassportCn::where('profile_id', $user['profileID'])->first();
        if ($record) {
            PassportCn::where('profile_id', $user['profileID'])->update($ocrRes);
        } else {
            $ocrRes['profile_id'] = $user['profileID'];
            PassportCn::create($ocrRes);
        }

        return responseSuccess();
    }


    /**
     * describe：银行卡有效期转换为时间戳
     * checkBankCardValidTime
     * @param $date
     * @return false|int|null
     * 2025/6/11 - Mark
     */
    public function checkBankCardValidTime($date)
    {
        // 匹配多种银行卡有效期格式
        $pattern = '/^((20)?\d{2})\/((0[1-9])|(1[0-2]))$/'; // 匹配 YYYY/mm 或 YY/mm
        $pattern1 = '/^((0[1-9])|(1[0-2]))\/((20)?\d{2})$/'; // 匹配 mm/YY 或 mm/YYYY

        $y = 0;
        $m = 0;

        if (preg_match($pattern, $date, $matches)) {
            // 匹配 YYYY/mm 或 YY/mm
            $yearPart = $matches[1];
            $monthPart = $matches[3];

            // 如果年份是两位数，补全为四位年份
            if (strlen($yearPart) == 2) {
                $yearPart = '20' . $yearPart; // 假设年份是 2000 年以后的
            }
            $y = $yearPart;
            $m = $monthPart;
        } else if (preg_match($pattern1, $date, $matches)) {
            // 匹配 mm/YY 或 mm/YYYY
            $monthPart = $matches[1];
            $yearPart = $matches[4];

            // 如果年份是两位数，补全为四位年份
            if (strlen($yearPart) == 2) {
                $yearPart = '20' . $yearPart; // 假设年份是 2000 年以后的
            }
            $y = $yearPart;
            $m = $monthPart;
        } else {

            return responseFail(__('bank card time error'));
        }

        // 将月份和年份转换为时间戳
        $validTime = strtotime("$y-$m-01");

        // 检查有效期是否已过
        if (time() >= $validTime) {
            return responseFail(__('bank card has expired'));
        }

        // 如果有效期有效
        return $validTime;
    }

    //上传国内银行卡
    public function uploadBankCardCn()
    {
        if (env('COUNTRY') != CountryModel::COUNTRY_ID_CHINA) {
            return responseFail();
        }
        $body = $this->request->all();
        $rules = [
            'file' => ['file', 'mimes:jpg,jpeg,png,webp,bmp,tiff', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
        ];
        $messages = [
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE') . 'M']),
        ];
        $validator = Validator::make($body, $rules, $messages);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors[0]);
        }
        $file = $this->request->file('file');
        $uploadPath = OssService::upload($file);
        if (!$uploadPath) {
            return responseFail();
        }
        $uploadUrl = OssService::link($uploadPath);
        $res = OcrService::bankCardCn($uploadUrl);
        if (!$res || $res['card_type'] != OcrService::BANK_CARD_TYPE_DC) {
            return responseFail(__('bank card photo error'));
        }
        $validateKey = ['bank_name', 'card_number', 'valid_to_date'];
        foreach ($validateKey as $k) {
            if (empty($res[$k])) {
                return responseFail(__('bank card other error'));
            }
        }
        $webBank = BankModel::where('bankCountry', 'CN')->where('bankNameZH', $res['bank_name'])->first();
        if (!$webBank) {
            return responseFail(__('bank not support'));
        }
        // 匹配多种银行卡有效期格式
        $pattern = '/^((20)?\d{2})\/((0[1-9])|(1[0-2]))$/'; // 匹配 YYYY/mm 或 YY/mm
        $pattern1 = '/^((0[1-9])|(1[0-2]))\/((20)?\d{2})$/'; // 匹配 mm/YY 或 mm/YYYY

        $y = 0;
        $m = 0;

        if (preg_match($pattern, $res['valid_to_date'], $matches)) {
            // 匹配 YYYY/mm 或 YY/mm
            $yearPart = $matches[1];
            $monthPart = $matches[3];

            // 如果年份是两位数，补全为四位年份
            if (strlen($yearPart) == 2) {
                $yearPart = '20' . $yearPart; // 假设年份是 2000 年以后的
            }
            $y = $yearPart;
            $m = $monthPart;
        } else if (preg_match($pattern1, $res['valid_to_date'], $matches)) {
            // 匹配 mm/YY 或 mm/YYYY
            $monthPart = $matches[1];
            $yearPart = $matches[4];

            // 如果年份是两位数，补全为四位年份
            if (strlen($yearPart) == 2) {
                $yearPart = '20' . $yearPart; // 假设年份是 2000 年以后的
            }
            $y = $yearPart;
            $m = $monthPart;
        } else {

            return responseFail(__('bank card time error'));
        }

        // 将月份和年份转换为时间戳
        $validTime = strtotime("$y-$m-01");

        // 检查有效期是否已过
        if (time() >= $validTime) {
            return responseFail(__('bank card has expired'));
        }

        $res['file'] = $uploadPath;
        $res['expire_time'] = $validTime - 1;
        $user = $this->request->attributes->get('user');
        $record = BankCardCn::where('profile_id', $user['profileID'])->first();
        if ($record) {
            BankCardCn::where('profile_id', $user['profileID'])->update($res);
        } else {
            $res['profile_id'] = $user['profileID'];
            BankCardCn::create($res);
        }
        // 用户确认后再提交更新
        // ProfileInfoModel::where('profileID', $user['profileID'])->update([
        //     'bank_id' => $webBank['ID'],
        //     'bank_account' => $res['card_number'],
        // ]);

        return responseSuccess(['bank_id' => $webBank['ID'], 'bank_account' => $res['card_number']]);
    }

    /**
     * 用户提交确认银行卡信息
     */
    public function confirmBankCard(Request $request)
    {
        $request->validate([
            'bank_id' => 'required|integer|exists:cna_web_bank,ID',
            'bank_account' => ['required', 'string', new CheckBankCardRule],
            'bank_branch' => 'required|string',
        ]);

        $user = $request->attributes->get('user');

        ProfileInfoModel::where('profileID', $user['profileID'])->update([
            'bank_id' => request('bank_id'),
            'bank_account' => request('bank_account'),
            'bank_branch' => request('bank_branch'),
        ]);
        (new SignatureService)->contractRedo($user['profileID']); //更新注册时的服务合同

        return responseSuccess();
    }

    public function isPartnerCode(Request $request)
    {
        $body = $request->validate([
            'code' => ['required'],
        ]);
        $exist = ProfileInfoModel::where('profilePartnerCode', $body['code'])->exists();
        if ($exist) {
            return responseSuccess();
        }

        return responseFail();
    }

    public function info(Request $request)
    {
        $user = $request->attributes->get('user');
        $profileId = $user['profileID'];
        $body = $request->validate([
            'profileID' => 'nullable|integer|min:1',
        ]);
        if (!empty($body['profileID'])) {
            $profileId = $body['profileID'];
        }
        $user = $this->profileInfo($profileId, $user);
        //获取上级信息
        $data['userInfo'] = [
            'profileID' => $user['profileID'],
            'profileName' => $user['profileName'],
            'profileEnglishName' => $user['profileEnglishName'],
            'profileAvatar' => $user['profileAvatar'],
            'profileGender' => $user['profileGender'],
            'profileBirthDate' => $user['profileBirthDate'],
            'profileContact' => $user['profileContact'],
            'profileEmail' => $user['profileEmail'],
            'profileAddressUnit' => $user['profileAddressUnit'],
            'profileAddressStreet' => $user['profileAddressStreet'],
            'profileAddressCountry' => $user['nationality']['countryZH'],
            'profilePartnerCode' => $user['profilePartnerCode'],
            'profileDesc' => $user['profileDesc'] ?? '',
            'edu' => $user['edu'] ?? '',
            'eduSubject' => $user['eduSubject'] ?? '',
            'eduDatetime' => $user['eduDatetime'] ?? '',
            'job' => $user['job'],
            'work_year' => $user['work_year'],
            'pre_id' => $user['pre_id'],
            'position' => $user['position'],
            'division' => str_replace(',', '', $user['district']['adname'] ?? ''),
            'profession' => $user['profession']['nameZH'] ?? '',
            'role_level' => $user['role_level'],
            'role_name' => ProfileInfoModel::ROLE_LEVEL_MAP[$user['role_level']],
            'settingLanguageAbility' => $user['settingLanguageAbility'],
            'profileNationalityID' => $user['profileNationalityID'],
        ];
        $data['parentInfo'] = [];
        if ($user->pre_id > 0) {
            $data['parentInfo'] = $this->profileInfoModel->select('profileName', 'profileEnglishName')
                ->where('profileID', $user->pre_id)
                ->first();
        }
        if ($request->method() == 'POST') {
            $qualifications = Qualification::select('education', 'certificate')
                ->where('profileID', $user->profileID)
                ->first();
            $data['education'] = empty($qualifications) ? [] : explode(',', $qualifications['education']);
            $data['certificate'] = empty($qualifications) ? [] : explode(',', $qualifications['certificate']);
            $data['workLicense'] = empty($qualifications) ? [] : explode(',', $qualifications['workLicense']);
            $data['businessCase'] = BusinessCase::where('type', BusinessCase::TYPE_CASE_NORMAL)
                ->where('profileID', $user->profileID)
                ->get();
            $data['AICase'] = BusinessCase::where('type', BusinessCase::TYPE_CASE_AI_TOOL)
                ->where('profileID', $user->profileID)
                ->get();
        }

        return responseSuccess($data);
    }
}
