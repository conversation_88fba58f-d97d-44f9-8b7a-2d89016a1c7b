<?php

namespace App\Services;

use App\Exceptions\AuthException;
use App\Models\ExamQuestionsModel;
use App\Models\ExamTypeModel;

class ExamQuestionsServices
{

    public function __construct(ExamQuestionsModel $examQuestionsModel)
    {
        $this->model = $examQuestionsModel;
    }

    /**
     * describe：获取指定业务的考试题目
     * getList
     * @param $param
     * @return array|null
     * 2025/6/17 - Mark
     */
    public function getList($param)
    {
        //根据业务id获取对应的考试
        $exam = ExamTypeModel::where('project_id', $param['project_service_data_id'])->with('question_num_config')->first();
        if (empty($exam)) {
            return responseFail('未找到对应的考试');
        }
        //获取每种类型题目的数量
        $question_num_config = $exam->question_num_config;

        //有配置题目
        if ($question_num_config->isNotEmpty()) {
            // 初始化结果数组
            $list = [];
            // 遍历每种题型，获取题目
            foreach ($question_num_config as $type => $config) {
                $query = $this->model->select('id', 'exam_id', 'type', 'question', 'option', 'score')
                    ->with(['exam:id,name'])
                    ->where('exam_id', $exam->id)
                    ->where('type', $config['question_type']);

                // 根据获取方式（随机或顺序）处理排序
                if ($param['get_type'] == 'random') {
                    $query->inRandomOrder();
                } else {
                    $query->orderBy('id', 'asc');
                }
                // 限制题目数量
                $query->limit($config['num']);
                // 获取结果并合并到最终数组
                $list = array_merge($list, $query->get()->toArray());
            }
            // 分离简答题和其他题目 简答题放到最后
            $shortAnswerQuestions = [];
            $otherQuestions = [];
            foreach ($list as $result) {
                if ($result['type'] == 2) {
                    $shortAnswerQuestions[] = $result;
                } else {
                    $otherQuestions[] = $result;
                }
            }
            // 合并其他题目和简答题
            $list = array_merge($otherQuestions, $shortAnswerQuestions);
        } else {
            $query = $this->model->select('id', 'exam_id', 'type', 'question', 'option', 'score')
                ->with(['exam:id,name'])
                ->where('exam_id', $exam->id);
            // 根据获取方式（随机或顺序）处理排序
            if ($param['get_type'] == 'random') {
                $query->inRandomOrder();
            } else {
                $query->orderBy('id', 'asc');
            }

            // 限制题目数量
            $query->limit($param['page_size'] ?? 10);

            // 获取结果并合并到最终数组
            $list = $query->get()->toArray();
        }


        return responseSuccess($list);

    }

    /**
     * describe：获取业务牌照题目
     * getPartnerFranchiseHandbookLicenseData
     * @param $param
     * @return array|null
     * 2025/6/19 - Mark
     */
    public function getPartnerFranchiseHandbookLicenseData($param)
    {
        // 根据业务id获取对应的所有课程考试
        $exam = (new ExamTypeModel())->where('project_id', $param['project_service_data_id'])->where('status', 1)->pluck('id')->toArray();
        if (empty($exam)) {
            return responseFail('未配置考试项目');
        }

        // 配置数组，定义每种题型的数量和排序方式
        //单选-10题-3分
        //多选-5题-4分
        //判断-10题-3分
        //答辩-1题-20分
        //（答辩题暂时用ai自己的评分标准测试效果）
        $questionTypes = [
            ExamQuestionsModel::TYPE_RADIO => ['limit' => 10, 'order' => 'id'],
            ExamQuestionsModel::TYPE_CHECKBOX => ['limit' => 5, 'order' => 'id'],
            ExamQuestionsModel::TYPE_JUDGE => ['limit' => 10, 'order' => 'id'],
            ExamQuestionsModel::TYPE_TXT => ['limit' => 1, 'order' => 'id'],
        ];

        // 初始化结果数组
        $list = [];

        // 遍历每种题型，获取题目
        foreach ($questionTypes as $type => $config) {
            $query = $this->model->select('id', 'exam_id', 'type', 'question', 'option', 'score')
                ->with(['exam:id,name'])
                ->whereIn('exam_id', $exam)
                ->where('type', $type);

            // 根据获取方式（随机或顺序）处理排序
            if ($param['get_type'] == 'random') {
                $query->inRandomOrder();
            } else {
                $query->orderBy($config['order'], 'asc');
            }

            // 限制题目数量
            $query->limit($config['limit']);

            // 获取结果并合并到最终数组
            $list = array_merge($list, $query->get()->toArray());
        }

        return responseSuccess($list);

    }

    /**
     * describe：晋级管理合伙人考试题目
     * getListData
     * @param $param
     * @return null
     * 2025/6/19 - Mark
     */
    public function getProfileUpgradePartnerManagerData($param)
    {
        //根据业务id获取对应的所有课程考试
        $exam = (new ExamTypeModel())->where('project_id', $param['project_service_data_id'])->where('status', 1)->pluck('id')->toArray();
        if (empty($exam)) {
            return responseFail('未配置考试项目');
        }
        $list = $this->model->select('id', 'exam_id', 'type', 'question', 'option', 'score')
            ->with(['exam:id,name'])
            ->whereIn('exam_id', $exam)
            ->when($param['get_type'] == 'random', function ($query) use ($param) {
                $query->inRandomOrder();
            })
            ->orderBy('id', 'asc')
            ->paginate($param['page_size'] ?? 10);

        return responseSuccess($list);
    }


    public function getList_old($param)
    {
        $param['page_size'] = $param['page_size'] ?? 10;
        $answer = [];
        $list = $this->model->select('id', 'exam_id', 'type', 'question', 'option')
            ->with(['exam:id,name'])
            ->when(!empty($param['type']) && $param['type'] != 0, function ($query) use ($param) {
                $query->where('type', $param['type']);
            });
        if (!empty($param['get_type']) && $param['get_type'] == 'random') {
            $items = $list->inRandomOrder()
                ->limit($param['page_size'])
                ->get()->map(function ($item) use (&$answer) {
                    $answer[] = $this->map($item);
                    return $item;
                });
            $paginate = [
                'page_size' => $param['page_size'],
                'total' => $param['page_size'],
                'total_page' => 1,
            ];
        } else {
            $list = $list->paginate($param['page_size'])
                ->through(function ($item) use (&$answer) {
                    $answer[] = $this->map($item);
                    return $item;
                });
            $items = $list->items();
            $paginate = [
                'page_size' => $param['page_size'],
                'total' => $list->total(),
                'total_page' => $list->lastPage(),
            ];
        }
        return compact('items', 'paginate', 'answer');
    }

    public function map(&$item, $answer = [])
    {
        $item = $item->toArray();
        $item['type_remark'] = $this->model::TYPE_REMARK[$item['type']];
        $item['option'] = json_decode($item['option'], true);
        shuffle($item['option']);
        // 动态生成选项标识
        foreach ($item['option'] as $index => &$option) {
            $option['option_key'] = chr(65 + $index);//这里确认在哪里确定ABCD再操作
            if ($option['is_answer']) {
                $answer[] = $option['option_key'];
            }
        }
        return $answer;
    }

    /**
     * 保存问题（新增或修改）
     * @param array $data 问题数据
     * @param int|null $id 问题ID（修改时必填）
     * @return ExamQuestionsModel
     * @throws \Exception
     */
    public function save(array $data, int $id = null)
    {
        // 检查题目是否重复
        $query = $this->model->where('question', $data['question']);

        if ($id) {
            // 修改时，排除自己的记录
            $query->where('id', '!=', $id);
        }

        if ($query->exists()) {
            throw new AuthException('该题目已存在');
        }

        if (empty($data['option'])) {
            throw new AuthException('题目缺少答案选项');
        }

        foreach ($data['option'] as $index => &$option) {
            // $option['option_key'] = chr(65 + $index);//这里确认在哪里确定ABCD再操作
            $option['is_checked'] = 0;
        }
        //$data['option'] = json_encode($data['option'], JSON_UNESCAPED_UNICODE);
        if ($id) {
            // 修改
            $question = $this->model->findOrFail($id);
            $question->update($data);
            return $question;
        } else {
            // 新增
            return $this->model->create($data);
        }
    }

    /**
     * 逻辑删除问题
     * @param int $id
     * @return void
     */
    public function destroy(int $id)
    {
        $question = ExamQuestionsModel::findOrFail($id);
        $question->destroy($id);
    }

}
