<?php

namespace App\Listeners;

use App\Events\PaymentEvent;
use App\Services\NotificationServices;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendPaymentNotification
{
    /**
     * Create the event listener.
     */
    public function __construct(NotificationServices $notificationServices)
    {
        $this->notificationServices = $notificationServices;
    }

    /**
     * Handle the event.
     */
    public function handle(PaymentEvent $event): void
    {
        try {
            switch ($event->userType) {//用户类型 0:合伙人 1:预备管理合伙人 2:管理合伙人
                case 0:
                    $data = [
                        'selfBusinessCommission' => $event->selfBusinessCommission,
                        'targetUser' => $event->userId,
                    ];
                    $user = [
                        'profileID' => $event->userId,
                    ];
                    $this->notificationServices->sendNotification($data, 'AdviceOfReceiptPartner', $user);
                    break;
                case 1:
                    $data = [
                        'selfBusinessCommission' => $event->selfBusinessCommission,
                        'KpiManagementAllowance' => $event->KpiManagementAllowance,
                        'targetUser' => $event->userId,
                    ];
                    $user = [
                        'profileID' => $event->userId,
                    ];
                    $this->notificationServices->sendNotification($data, 'AdviceOfReceiptPrepare', $user);
                    break;
                case 2:
                    $data = [
                        'selfBusinessCommission' => $event->selfBusinessCommission,
                        'KpiManagementAllowance' => $event->KpiManagementAllowance,
                        'targetUser' => $event->userId,
                    ];
                    $user = [
                        'profileID' => $event->userId,
                    ];
                    $this->notificationServices->sendNotification($data, 'AdviceOfReceiptManage', $user);
                    break;

            }

        } catch (\Exception $e) {
            Log::info('发送款项通知失败', [$e->getMessage()]);
        }
    }
}
