<?php

namespace App\Models\Course;

use App\Models\ProfileInfoModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProfileExam extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'cna_profile_exam';
    protected $guarded = [];

    //指定字段是数组
    protected $casts = [
        'detail' => 'array',
    ];

    //时间转换
    protected function serializeDate(\DateTimeInterface $date){
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }
}