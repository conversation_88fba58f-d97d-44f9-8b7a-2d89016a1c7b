<?php

namespace App\Models\Course;

use App\Models\ProfileInfoModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectServiceCourseIntro extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'cna_project_service_course_intro';
    protected $fillable = [
        'id',
        'detail',
        'type',
        'service_data_id'
    ];

    //时间转换
    protected function serializeDate(\DateTimeInterface $date){
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }
}