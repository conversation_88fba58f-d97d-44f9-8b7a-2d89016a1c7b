<?php

namespace App\Http\Controllers\Api\v1\Business;

use App\Models\Course\ProfileExam;
use App\Models\ExamQuestionsModel;
use App\Models\ProjectServiceDataModel;
use App\Models\Question;
use App\Services\Business\ExamService;
use Illuminate\Http\Request;

class ExamController
{
    /**
     * describe：新增用户考试结果
     * result
     * @param Request $request
     * @param ExamService $service
     * @return void
     * 2025/6/14 - Mark
     */
    public function result(Request $request, ExamService $service)
    {
        $data = $request->validate([
            'data' => 'required|array',
            'data.*.question_id' => function ($attribute, $value, $fail) {
                $exists = (new ExamQuestionsModel())->where('id', $value)->exists();
                if (!$exists) {
                    $fail("未找到指定的考题, ID: {$value}");
                }
            },
            'data.*.answer' => 'required',
        ]);
        $user = $request->attributes->get('user');
        $info = $service->result($user, $data['data']);
        return $info;
    }

    /**
     * describe：获取考试结果
     * getResult
     * @param Request $request
     * @return null
     * 2025/6/14 - Mark
     */
    public function getResult(Request $request)
    {
        $data = $request->validate([
            'exam_id' => 'required|exists:cna_exam_type,id',
            'project_service_data_id' => 'required|exists:cna_project_service_data,id',
        ]);
        $user = $request->attributes->get('user');
        $info = (new ProfileExam())
            ->where('profileID', $user->profileID)
            ->where('exam_id', $data['exam_id'])
            ->where('project_service_data_id', $data['project_service_data_id'])
            ->first();

        return responseSuccess($info);
    }

    /**
     * describe：检查用户是否可以考试（是否所有课程进度100%）
     * checkCanExam
     * @param Request $request
     * @return void
     * 2025/7/2 - Amos
     */
    public function checkCanExam(Request $request)
    {
        $request->validate([
            'service_data_id' => 'required|exists:cna_project_service_data,id',
        ]);

        $user = $request->attributes->get('user');

        // 检查是否有未完成的课程
        $service = ProjectServiceDataModel::find($request->service_data_id);

        $hasUnfinishedCourses = $service->courses()->profileCourse()
            ->where('profile_id', $user['profileID'])
            ->where('progress', '<', 100)
            ->exists();

        if ($hasUnfinishedCourses) {
            return responseFail('请先完成所有课程');
        }

        return responseSuccess();
    }
}
