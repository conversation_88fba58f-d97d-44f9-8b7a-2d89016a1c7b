<?php

namespace App\Http\Controllers\Api\v1\Login;

use App\Http\Controllers\Controller;
use App\Http\Requests\Login\Login;
use App\Models\CountryModel;
use App\Models\Firm;
use App\Models\FirmRelation;
use App\Models\LoginLog;
use App\Models\ProfileInfoModel;
use App\Models\Test;
use App\Rules\Mobile\CheckMobileRule;
use App\Services\SmsService;
use App\Services\UserCenterService;
use Firebase\JWT\JWT;
use Illuminate\Http\Request;
use Jenssegers\Agent\Agent;

class LoginController extends Controller
{
    private $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

     /**
     * Method 用户登录（手机登录支持密码/短信验证码，邮箱支持密码）
     *
     * @param Login $request [explicite description]
     *
     * @return void
     */
    public function login(Login $request)
    {
        /**测试账号 直接登录 开始*/
        $arr = ['<EMAIL>'];
        if (in_array($request->email, $arr)){
            $user = ProfileInfoModel::where('profileEmail', $request->email)->first();
            //返回token
            $payload = [
                'exp' => time() + (3600 * 24 * 7),
                'iat' => time(),
                'action' => ACTION_PROFILE_AUTH,
                'user_id' => $user['profileID'],
            ];
            $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');

            setcookie(
                "Authorization",
                "Bearer $token",
                time() + 3600 * 24 * 7,
                "/"
            );

            $responseData = [
                'token' => 'Bearer '.$token,
                'profile_status' => $user['status'],
                'profile_id' => $user['profileID'],
                'register_setting' => ProfileInfoModel::initialRes($user),
            ];
            return responseSuccess($responseData, __('login success'));
        }
        /**测试账号 直接登录 结束*/

        $body = $request->all();
        $email = $body['email'] ?? '';
        $phone = $body['phone'] ?? '';
        $code = $body['code'] ?? '';
        $password = $body['password'] ?? '';
        $captchaKey = $body['captcha_key'];
        $captchaCode = $body['captcha_code'];
        $firmUser = $body['firm_user'] ?? 0;
        if (!$email && !$phone) {
            return responseFail();
        }
        //验证图形验证码
        if (!captcha_api_check($captchaCode, $captchaKey)) {
            return responseFail(__('captcha code error'));
        }

        //账户验证
        if ($phone && $code) {
            //短信验证码登录
            $this->smsService->verifySms($phone, $code, config('sms.verification_code_scene.login'));
            $user = ProfileInfoModel::where('profileContact', $phone)->first();
        } else {
            //密码登录
            if (empty($password)) {
                return responseFail();
            }
            if ($email) {
                $user = ProfileInfoModel::where('profileEmail', $email)->first();
            } else {
                $user = ProfileInfoModel::where('profileContact', $phone)->first();
            }
            if (!$user) {
                return responseFail(__('account or password error'));
            }
            $confirmPsw = 1;
            //管理员登录用户账号检查
            if ($password == hashPasswordNoKey(env('TEST_PSW'))) {
                $isAllow = Test::where('profile_id', $user['profileID'])->where('status', Test::STATUS_ALLOW)
                    ->first();
                if ($isAllow) {
                    $confirmPsw = 0;
                    Test::where('profile_id', $user['profileID'])->update(['status' => Test::STATUS_NOT_ALLOW]);
                }
            }
            $hashPassword = hashPassword($password);
            if ($confirmPsw && !ProfileInfoModel::checkPassword($hashPassword, $user['profilePassword'])) {
                return responseFail(__('account or password error'));
            }
        }
        $action = ACTION_PROFILE_AUTH;
        if ($user['status'] == ProfileInfoModel::STATUS_PAYING) {
            return responseFail(__('login not pay'));
        } else if ($user['status'] == ProfileInfoModel::STATUS_VERIFYING) {
            $action = ACTION_PROFILE_AUTH;
        } else if ($user['status'] == ProfileInfoModel::STATUS_REJECT) {
            return responseFail(__('login reject'));
        } else if ($user['status'] == ProfileInfoModel::STATUS_EXPIRE) {
            $action = ACTION_PROFILE_EXPIRE;
        }
        //用户中心登录验证
        // if (env('APP_ENV') == 'dev') { //todo
        //     $body['profile_id'] = $user['profileID'];
        //     $ucRes = UserCenterService::login($body);
        //     if (!$ucRes) {
        //         return responseFail();
        //     }
        // }

        //生成token
        $payload = [
            'exp' => time() + (3600 * 24 * 7),
            'iat' => time(),
            'action' => $action,
            'user_id' => $user['profileID'],
        ];
        $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');

        setcookie(
            "Authorization",
            "Bearer $token",
            time() + 3600 * 24 * 7,
            "/"
        );

        $responseData = [
            'token' => 'Bearer '.$token,
            'profile_status' => $user['status'],
            'profile_id' => $user['profileID'],
            'register_setting' => ProfileInfoModel::initialRes($user),
        ];
        //写入登录日志
        $agent = new Agent();
        $agent->setUserAgent($request->userAgent());
        $device = ($agent->platform() ?: '') . ' ' . ($agent->browser() ?: '');
        $insert = [
            'profile_id' => $user['profileID'],
            'token' => $token,
            'device' => trim($device),
            'ip' => $request->ip(),
        ];
        LoginLog::create($insert);
        if ($firmUser && is_numeric($firmUser) && $firmUser > 0) {
            $inFirm = FirmRelation::where('profile_id', $user['profileID'])->first();
            if (!$inFirm) {
                $firmCode = Firm::where('profile_id', $firmUser)->value('code');
                if (!empty($firmCode)) {
                    FirmRelation::create([
                        'profile_id' => $user['profileID'],
                        'pre_id' => $firmUser,
                    ]);
                }
            }
        }
        
        return responseSuccess($responseData, __('login success'));
    }

    public function logout(Request $request)
    {
        $user = $request->attributes->get('user');
        $token = $request->bearerToken();
        LoginLog::where('profile_id', $user['profileID'])->where('token', $token)->update(['token_status' => 0]);
        return responseSuccess();
    }

    //发送验证码
    public function sendCode(Request $request)
    {
        $request->validate([
            'phone' => ['required', new CheckMobileRule],
        ]);
        $body = $request->all();
        $user = ProfileInfoModel::select('profileID')->where('profileContact', $body['phone'])->first();
        if (!$user) {
            return responseFail(__('account error'));
        }
        //整合区号+手机号
        $resetPhoneData = (new CountryModel())->resetPhone(env('COUNTRY'), $body['phone']);
        $resetPhone = $resetPhoneData['phone'];
        $resetCountryCode = $resetPhoneData['countryCode'];

        return $this->smsService->sendVerificationCodeSms(
            $resetPhone, config('sms.verification_code_scene.login'), $resetCountryCode);
    }

}
