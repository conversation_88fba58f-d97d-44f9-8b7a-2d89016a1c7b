<?php

namespace App\Services;

use App\Models\PaymentModel;
use App\Models\ProfileAdvanceCommissionLogModel;
use App\Models\ProfileAdvanceCommissionModel;

class ProfileAdvanceCommissionServices {
    public function __construct(ProfileAdvanceCommissionModel $advanceCommissionModel,CurrencyServices $currencyServices) {
        $this->model = $advanceCommissionModel;
        $this->currencyServices = $currencyServices;
    }

    public function info($id){
        return $this->model->find($id);
    }

    public function create($profileID,$advancePrice,$currency_id,$logMessage='创建记录',$process = null){
        $currency = $this->currencyServices->getInfo($currency_id);
        if( empty($currency) ){
            throw new \AuthException('汇率信息获取错误');
        }
        //转换成人民币
        $logMessage .= '预支 '.$advancePrice.' '.$currency->currency;
        $advancePrice = bcmul($advancePrice,$currency->rate,2);
        $data = $this->model->create([
            'profile_id' => $profileID,
            'advance_price' => $advancePrice,
            'remark' => $logMessage
        ]);
        $this->log($data->id,$advancePrice,0,$currency_id,$currency_id,$currency->rate,$logMessage);
        //记录payment 数据项
        $paymentData = [
            'profileID' => $profileID,
            'type' => PaymentModel::TYPE_COMMISSION_EXPENSE,
            'projectId' => 0,
            'detailId' => 0,
            'fee' => $advancePrice,
            'remark' => $logMessage,
        ];
        if( $process ){//存在进程data 配置项目挂靠信息
            // 验证并获取项目服务数据
            $projectServiceData = app(ProjectServiceDataServices::class)->getProjectServiceData($process['project_id']);
            if (!empty($projectServiceData)) {
                $paymentData['projectId'] = $projectServiceData->projectId;
            }
            $paymentData['detailId'] = $process['project_id'];
        }
        app(PaymentServices::class)->createPaymentItem($paymentData,$process);
    }

    public function change($profileID,$changePrice,$currency_id,$logMessage='',$process = null){
        $where = ['profile_id'=>$profileID];
        $data = $this->model->where($where)->first();
        if( empty($data) ){
            throw new \AuthException('信息获取失败');
        }
        $data = $data->toArray();
        $currency = $this->currencyServices->getInfo($currency_id);
        $logMessage .= $changePrice < 0 ? '预支:' : '抵扣:'.$changePrice.' '.$currency->currency;
        //需要根据汇率信息计算 转换成人民币
        $changePrice = bcmul($changePrice,$currency->rate,2);
        $updateData = [
            'advance_price' => bcsub($data['advance_price'],$changePrice,2),
            'recoup_price' => bcadd($data['recoup_price'],$changePrice,2),
        ];
        $this->model->where($where)->update($updateData);
        $this->log($data->id,$changePrice,$data['advance_price'],$data['currency_id'],$currency_id,$currency->rate,$logMessage);
    }

    protected function log($advanceId, $changePrice,$oldPrice,$advanceCurrencyId,$changeCurrencyId,$currencyRate,$remark='')
    {
        return ProfileAdvanceCommissionLogModel::create([
            'advance_commission_id' => $advanceId,
            'change_price' => $changePrice,
            'old_advance_price' => $oldPrice,
            'change_currency_id' => $changeCurrencyId,
            'rate' => $currencyRate,
            'remark' => $remark
        ]);
    }
}