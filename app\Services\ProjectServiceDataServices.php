<?php

namespace App\Services;

use App\Exceptions\AuthException;
use App\Models\ProjectServiceDataModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ProjectServiceDataServices {
    private $model;

    public function __construct(ProjectServiceDataModel $projectServiceModel) {
        $this->model = $projectServiceModel;
    }

    public function getCodeList($param,$orderBy='',$isGetCate=false){
        $list = $this->model->select('id','title_zh as project_name','currency as pay_currency','price','projectId','case','currency_id','code')
            ->where('status',ProjectServiceDataModel::STATUS_OPEN)
            ->when($isGetCate,function($query) use ($param){
            })
            ->when(!empty($param['project_code']),function($query) use ($param){
                $query->where('code',$param['project_code']);
            })
            ->when(!empty($param['project_name']),function($query) use ($param){
                $query->where('title_zh','like','%'.$param['project_name'].'%');
            })
            ->when(!empty($param['codeIn']),function($query) use ($param) {
                $query->whereIn('code', $param['codeIn']);
            });
        $with = ['currencyInfo:id,currency,currency_code,rate'];
        if($isGetCate){
            $with[] = 'category';
        }
        $list->with($with);
        if( $orderBy ){
            $list->orderByRaw($orderBy);
        }else{
            $list->orderBy("id","asc");
        }
        return $list->get()->map(function($item){
            if (!empty($item['currencyInfo']) && isset($item['currencyInfo']['rate'])) {
                $item['CNY_price'] = bcmul($item['price'],$item['currencyInfo']['rate'],2);
            } else {
                $item['CNY_price'] = 0; // 或其他默认值
            }
            return $item;
        });
    }

    public function getInfoByCode($code)
    {
        return $this->model->select('id','title_zh','currency','price','projectId','case','code','currency_id','status','is_exempt','payment_main_id','merge_project_id')
            ->with(['category','currencyInfo','paymentMain'])
            ->where('code', $code)
            ->first();
    }

    /**
     * describe：获取项目服务数据
     * getProjectServiceData
     * @param $id
     * @return Builder|Model|object|null
     * 2025/6/12 - Mark
     */
    public function getProjectServiceData($id){
        $data = $this->model->select('id','title_zh','currency','price','projectId','case','code','currency_id','status','is_exempt','payment_main_id','merge_project_id')
            ->with(['category','currencyInfo']);
        if( is_array($id) ){
            $list = $data->whereIn('id', $id)->get();
        }else{
            $list = $data->where('id', $id)->first();
        }
        return $list;
    }
}