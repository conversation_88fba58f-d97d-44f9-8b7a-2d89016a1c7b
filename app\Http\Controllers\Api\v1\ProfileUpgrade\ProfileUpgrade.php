<?php

namespace App\Http\Controllers\Api\v1\ProfileUpgrade;

use App\Http\Controllers\Controller;
use App\Models\ProjectServiceDataModel;
use App\Services\ProfileBusinessProcessServices;
use App\Services\ExamQuestionsServices;
use App\Services\ProjectServiceDataServices;
use Illuminate\Http\Request;

class ProfileUpgrade extends Controller
{
    public function __construct(ExamQuestionsServices $examQuestionsServices)
    {
        $this->services = $examQuestionsServices;
    }

    public function examQuestions(Request $request)
    {
        $param = $request->validate([
            'page' => 'bail|nullable|integer',
            'page_size' => 'required|integer',
            'get_type' => 'bail|nullable|string',
            'project_service_data_id' => 'bail|required',
        ]);
        //$param['type'] = 1;
        return $this->services->getList($param);
    }

    public function submitExam(Request $request,ProfileBusinessProcessServices $profileBusinessProcessServices)
    {
        $user = $request->attributes->get('user');
        //更新业务状态
        $process = $profileBusinessProcessServices->getByProfileID($user->profileID,'profile_upgrade_partner_manager');
        if(empty($process)){
            return responseFail('未参与当前业务');
        }
        //更新业务状态
        $res = $profileBusinessProcessServices->finished($process->toArray());
        //更新用户身份
        return responseSuccess();
    }

    public function getMenu(Request $request, ProjectServiceDataServices $projectServiceDataServices){
        $user = $request->attributes->get('user');
        //检查用户状态是否符合当前业务
        //已获取资格 或者 已是合伙人 不进入此流程
        $res = [
            'status' => 0,
        ];
        if( ( $user->has_manager_power && $user->role_level == 0 ) || $user->role_level > 0  ){
            $res['status'] = 1;
            return responseSuccess($res);
        }
        $upgradeProjectCode = ProjectServiceDataModel::UPGRADE_PROJECT_CODE;
        $orderBy = "FIELD(code, '".implode("','", $upgradeProjectCode)."')";
        $menu = $projectServiceDataServices->getCodeList(['codeIn'=>$upgradeProjectCode],$orderBy);
        foreach( $menu as &$item ){
            $item['name'] = ProjectServiceDataModel::UPGRADE_PROJECT_NAME[$item['code']];
        }
        $res['menu'] = $menu;
        return responseSuccess($res);
    }
}