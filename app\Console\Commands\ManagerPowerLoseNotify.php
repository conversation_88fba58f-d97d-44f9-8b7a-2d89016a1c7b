<?php

namespace App\Console\Commands;

use App\Models\ProfileInfoModel;
use App\Services\NotificationServices;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ManagerPowerLoseNotify extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'manager-power-lose-notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '失去晋升管理合伙人资格付款通知 失去后一天发送';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            //查询的日期
            $date = date('Y-m-d', strtotime('-1 day'));

            //查询非管理员 且 有晋升资格 时间是昨天 的用户 （已失效 失效提醒）
            $users = ProfileInfoModel::where('role_level', '!=', ProfileInfoModel::ROLE_PARTNER_MANAGER)
                ->whereNotNull('manager_prower_start_time')
                ->whereDate('manager_power_expire_time', $date)
                ->get();
            if ($users->isNotEmpty()) {
                foreach ($users as $user) {
                    $data = [
                        'targetUser' => $user->profileID,
                    ];
                    $templateCode = 'LosePromotion';
                    //发送失去晋级资格通知
                    (new NotificationServices())->send($templateCode, $data, $user->toArray());
                    //未付款的发送付款提醒
                    if($user->role_level == ProfileInfoModel::ROLE_NORMAL){
                        $data = [
                            'targetUser' => $user->profileID,
                        ];
                        $templateCode = 'PaymentOrder';
                        //发送失去晋级资格通知
                        (new NotificationServices())->send($templateCode, $data, $user->toArray());
                    }
                }

            }
        } catch (\Exception $e) {
            Log::info('发送【合伙人失去晋升资格】通知失败：', [$e->getMessage()]);
        }

    }
}
