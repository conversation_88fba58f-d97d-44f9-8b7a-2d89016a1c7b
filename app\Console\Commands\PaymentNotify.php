<?php

namespace App\Console\Commands;

use App\Events\PaymentEvent;
use App\Models\ProfileInfoModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PaymentNotify extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment-notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '收款通知 一个月一次';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            //TODO 查询用户款项信息  自身业务分成  KPI管理津贴  用户类型 0:合伙人 1:预备管理合伙人 2:管理合伙人
            //事件调用 发送通知
            //event(new PaymentEvent(1, 0, 0, 0));
        }catch (\Exception $e){
            Log::info('发送【合伙人失去晋升资格】通知失败：',[$e->getMessage()]);
        }

    }
}
