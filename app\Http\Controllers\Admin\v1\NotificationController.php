<?php

namespace App\Http\Controllers\Admin\v1;

use App\Http\Controllers\Controller;
use App\Models\ActiveLog;
use App\Models\NotificationInfoModel;
use App\Models\ProfileInfoModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    /**
     * 通知列表
     * @param Request $request
     * @return null
     */
    public function index(Request $request)
    {
        $user = $request->attributes->get('user');
        $pageSize = $request->get('page_size', 10);
        $keyword = $request->get('keyword'); // 关键字
        $list = NotificationInfoModel::query()->where('notificationTarget',$user['profileID'])->when($keyword != '', function ($query) use ($keyword) {
            $query->where(function ($query) use ($keyword) {
                $query->orWhere('notificationTitleZH', 'like', '%' . $keyword . '%')
                    ->orWhere('notificationTitleEN', 'like', '%' . $keyword . '%')
                    ->orWhere('notificationTitleMS', 'like', '%' . $keyword . '%')
                    ->orWhere('notificationTitleZT', 'like', '%' . $keyword . '%')
                    ->orWhere('notificationDescriptionEN', 'like', '%' . $keyword . '%')
                    ->orWhere('notificationDescriptionMS', 'like', '%' . $keyword . '%')
                    ->orWhere('notificationDescriptionZH', 'like', '%' . $keyword . '%')
                    ->orWhere('notificationDescriptionZT', 'like', '%' . $keyword . '%')
                    ->orWhere('notificationDepartment', 'like', '%' . $keyword . '%')
                    ->orWhere(function ($query) use ($keyword) { // 根据用户名查找
                        $profileIds = ProfileInfoModel::Where('profileName', 'like', '%' . $keyword . '%')->pluck('profileID')->all();
                        $query->whereIn('notificationTarget', $profileIds);
                    });
            });
        })->orderBy('createTime', 'desc')->paginate($pageSize);

        $items = collect($list->items())->map(function ($item) {
            // 发送对象
            $item->departmentName = $item->notificationDepartment ? NotificationInfoModel::getNotificationDepartment($item->notificationDepartment) : null;

            return $item;
        });

        $paginate = [
            'page_size' => $pageSize,
            'total' => $list->total(),
            'total_page' => $list->lastPage(),
        ];

        return responseSuccess(compact('items', 'paginate'));
    }

    /**
     * 创建公告
     * @param Request $request
     * @return void
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'notificationTitleZH'       => 'required',
            'notificationDescriptionZH' => 'required',
            'notificationTitleEN'       => 'required',
            'notificationDescriptionEN' => 'required',
            'notificationTitleMS'       => 'required',
            'notificationDescriptionMS' => 'required',
            'notificationTitleZT'       => 'required',
            'notificationDescriptionZT' => 'required',
            'type'                      => 'required',
            'notificationValidity'      => 'required',
            'createTime'                => 'required'
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 参数
        $user = $request->attributes->get('user');
        $params = $request->all();
/*
        if (!in_array($params['type'], [1,3])) {
            return responseFail('admin.common.param.status_error');
        }*/

        if (strtotime($params['notificationValidity']) < time()) { // 有效期小于当前时间
            return responseFail(__('Validity less the current time'));
        }

        $result = NotificationInfoModel::query()->create([
            'notificationTitleZH'       => $params['notificationTitleZH'],
            'notificationDescriptionZH' => $params['notificationDescriptionZH'],
            'notificationTitleEN'       => $params['notificationTitleEN'],
            'notificationDescriptionEN' => $params['notificationDescriptionEN'],
            'notificationTitleMS'       => $params['notificationTitleMS'],
            'notificationDescriptionMS' => $params['notificationDescriptionMS'],
            'notificationTitleZT'       => $params['notificationTitleZT'],
            'notificationDescriptionZT' => $params['notificationDescriptionZT'],
            'notificationValidity'      => $params['notificationValidity'],
            'notificationTarget'        => 0,
            'notificationDepartment'    => $params['type'], // 发送角色：1合伙人，3律师
            'createUser'                => $user['profileID'],
            'createTime'                => $params['createTime'],
            'createRole'                => $user['profileRole']
        ]);
        if ($result) {
            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_CREATE, ActiveLog::ADMIN_API_V1_NOTIFY,
                $result->notificationID,$params['notificationTitleZH'], ActiveLog::SYSTEM_CNA_ADMIN);
            return responseSuccess();
        } else {
            return responseFail('admin.common.send_failed');
        }
    }

    /**查看通知
     * @param $id
     * @return void
     */
    public function edit(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = NotificationInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        // 获取上一条和下一条
        $prevID = NotificationInfoModel::where('createTime', '<', $info->createTime)
            ->orderBy('createTime', 'desc')
            ->first(['notificationID']);

        $nextID = NotificationInfoModel::where('createTime', '>', $info->createTime)
            ->orderBy('createTime', 'asc')
            ->first(['notificationID']);

        $paginate = [
            'prev_id' => $prevID ? $prevID->notificationID : null,
            'next_id' => $nextID ? $nextID->notificationID : null
        ];

        return responseSuccess(compact('info', 'paginate'));
    }

    /**
     * 更新通知
     * @param Request $request
     * @return void
     */
    public function update(Request $request, $id)
    {
        if (empty($id)) {
            return responseFail(__('param error', ['param' => 'id']));// 参数缺失:id
        }

        // 获取详情
        $info = NotificationInfoModel::find($id);
        if (empty($info)) {
            return responseFail(__('info no exist'));// 信息不存在
        }

        $validator = Validator::make($request->all(), [
            'notificationTitleZH'       => 'required',
            'notificationDescriptionZH' => 'required',
            'notificationTitleEN'       => 'required',
            'notificationDescriptionEN' => 'required',
            'notificationTitleMS'       => 'required',
            'notificationDescriptionMS' => 'required',
            'notificationTitleZT'       => 'required',
            'notificationDescriptionZT' => 'required',
            'type'                      => 'required',
        ]);

        // 验证失败
        if ($validator->fails()) {
            return responseFail($validator->errors()->all());
        }

        // 参数
        $user = $request->attributes->get('user');
        $params = $request->all();

       /* if (!in_array($params['type'], [1,3])) {
            return responseFail('admin.common.param.status_error');
        }*/


        $result = $info->update([
            'notificationTitleZH'       => $params['notificationTitleZH'],
            'notificationDescriptionZH' => $params['notificationDescriptionZH'],
            'notificationTitleEN'       => $params['notificationTitleEN'],
            'notificationDescriptionEN' => $params['notificationDescriptionEN'],
            'notificationTitleMS'       => $params['notificationTitleMS'],
            'notificationDescriptionMS' => $params['notificationDescriptionMS'],
            'notificationTitleZT'       => $params['notificationTitleZT'],
            'notificationDescriptionZT' => $params['notificationDescriptionZT'],
            'notificationValidity'      => !empty($params['notificationValidity'])?$params['notificationValidity']:$info['notificationValidity'],
            'notificationTarget'        => 0,
            'notificationDepartment'    => $params['type'], // 发送角色：1合伙人，3律师
            'createTime'                => !empty($params['createTime'])?$params['createTime']:$info['createTime'],
            'editUser'                  => $user['profileID'],
            'editTime'                  => date('Y-m-d H:i:s'),
            'editRole'                  => $user['profileRole']
        ]);
        if ($result) {
            // 记录日志
            ActiveLog::log($user['profileID'], ActiveLog::ADMIN_API_V1_EDIT, ActiveLog::ADMIN_API_V1_NOTIFY,
                $id,$params['notificationTitleZH'], ActiveLog::SYSTEM_CNA_ADMIN);

            return responseSuccess();
        } else {
            return responseFail(__('edit failed'));
        }
    }


}
