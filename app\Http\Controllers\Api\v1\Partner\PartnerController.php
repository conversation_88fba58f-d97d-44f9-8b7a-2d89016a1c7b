<?php

namespace App\Http\Controllers\Api\v1\Partner;

use App\Http\Controllers\Controller;
use App\Models\Interview;
use App\Models\ProfileInfoModel;
use Illuminate\Http\Request;

class PartnerController extends Controller
{
    public function list(Request $request)
    {
        $body = $request->validate([
            'pageSize' => ['integer', 'min:1'],
        ]);
        $user = $request->attributes->get('user');
        $data = ProfileInfoModel::where('pre_id', $user['profileID'])
            ->whereIn('status', [ProfileInfoModel::STATUS_VERIFYING, ProfileInfoModel::STATUS_ACTIVE])
            ->orderBy('profileID', 'desc')
            ->paginate($body['pageSize'] ?? 10);
        $userIds = $data->pluck('profileID')->toArray();
        if ($userIds) {
            $interviews = Interview::whereIn('user_id', $userIds)->pluck('status', 'user_id');
            foreach ($data as &$item) {
                $item['interview_status'] = $interviews[$item['profileID']] ?? 0;
            }
        }

        return responseSuccess($data);
    }
}
