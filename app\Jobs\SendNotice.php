<?php

namespace App\Jobs;

use App\Services\NotificationServices;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendNotice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $data;
    public $type;
    protected $user;

    /**
     * @param array $data 通知数据，包含模板变量和目标用户信息
     * @param string $type 通知类型代码，对应模板表中的eventCode
     * @param array $user 操作用户信息，包含profileID和profileRole
     */
    public function __construct($data, $type, $user = [])
    {
        $this->data = $data;
        $this->type = $type;
        $this->user = $user;
    }

    /**
     * 执行通知发送任务
     * @return bool
     */
    public function handle(): bool
    {
        $notificationServices = app(NotificationServices::class);
        return $notificationServices->sendNotification($this->data, $this->type, $this->user);
    }
}

