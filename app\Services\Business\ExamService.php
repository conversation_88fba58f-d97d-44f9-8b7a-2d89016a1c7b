<?php

namespace App\Services\Business;

use App\Models\Course\ProfileExam;
use App\Models\ExamQuestionsModel;
use App\Models\ExamTypeModel;
use App\Models\ProfileInfoModel;
use App\Services\AiService;

class ExamService
{
    /**
     * describe：新增用户考试结果
     * result
     * @param $user
     * @param $data
     * @return null
     * 2025/6/14 - Mark
     */
    public function result($user, $data)
    {
        try {
            $detail = $this->getScore($data);

            //获取第一个题目的id 获取对应的考试id  根据考试id获取到业务的id
            $question = (new ExamQuestionsModel())->where('id', $data[0]['question_id'])->first();
            //记录用户考试结果
            $profileExam = new ProfileExam();
            $profileExam->profileID = $user->profileID;
            $profileExam->project_service_data_id = (new ExamTypeModel())->where('id', $question->exam_id)->value('project_id') ?? 0;
            $profileExam->pass = $detail['totalScore'] >= 80 ? 1 : 2;
            $profileExam->score = $detail['totalScore'];
            $profileExam->detail = $detail;
            $profileExam->save();

            $back = [
                'total_score' => $detail['totalScore'],
                'pass' => $detail['totalScore'] >= 80 ? 1 : 2,
                'detail' => $detail['detail'],
            ];
        } catch (\Exception $e) {
            return responseFail($e->getMessage());
        }

        return responseSuccess($back);
    }


    /**
     * describe：根据题目数组获取总分和详情
     * getScore
     * @param $data
     * @return array|null
     * 2025/6/18 - Mark
     */
    public function getScore($data)
    {
        $questionIds = array_column($data, 'question_id');
        $questions = (new ExamQuestionsModel())->whereIn('id', $questionIds)->get(['id', 'question', 'type', 'score', 'option'])->keyBy('id');
        $totalScore = 0;
        $detail = [];

        foreach ($data as $item) {
            $question = $questions[$item['question_id']] ?? null;
            if (!$question) {
                continue; // 如果题目不存在，跳过
            }

            switch ($question->type) {
                case 0: // 单选题
                    $score = $this->handleSingleChoiceOrJudgment($question, $item['answer']);
                    $totalScore += $score;
                    $detail[] = $this->makeProfileAnswerData($question, $item['answer'], $score);
                    break;
                case 1: // 多选题
                    $userAnswer = explode('@', $item['answer']);
                    if (!is_array($userAnswer) || count($userAnswer) == 0) {
                        break;
                    }
                    $correctAnswer = array_column(array_filter($question->option, fn($opt) => $opt['is_answer'] == 1), 'option_name');
                    if (count(array_diff($userAnswer, $correctAnswer)) == 0 && count($userAnswer) == count($correctAnswer)) {
                        $score = $question->score;
                    } else {
                        $score = 0;
                    }
                    $totalScore += $score;
                    $detail[] = $this->makeProfileAnswerData($question, $item['answer'], $score);
                    break;
                case 2: //文本题
                    $data = [
                        'question'=>$question->question,
                        'answer'=> $item['answer']
                    ];
                    //丢给AI 返回分数和评价
                    $info = (new AiService())->getShortAnswerScore($data);
                    $totalScore += $info['score'];
                    $detail[] = $this->makeProfileAnswerData($question, $item['answer'], $info['score'], $info['comment']);
                    break;
                case 3: // 判断题
                    $score = $this->handleSingleChoiceOrJudgment($question, $item['answer']);
                    $totalScore += $score;
                    $detail[] = $this->makeProfileAnswerData($question, $item['answer'], $score);
                    break;
                default:
                    return responseFail('未知的题型');
                    break;
            }
        }
        return ['totalScore' => $totalScore, 'detail' => $detail];
    }

    /**
     * describe：返回分数
     * handleSingleChoiceOrJudgment
     * @param $question
     * @param $answer
     * @return int|mixed
     * 2025/6/18 - Mark
     */
    private function handleSingleChoiceOrJudgment($question, $answer)
    {
        foreach ($question->option as $option) {
            if ($option['option_name'] == $answer && $option['is_answer'] == 1) {
                return $question->score;
            }
        }
        return 0;
    }

    /**
     * describe：组装用户答案数据
     * makeProfileAnswerData
     * @param $question
     * @param $answer
     * @param int $score
     * @param string $comment
     * @return array
     * 2025/6/20 - Mark
     */
    public function makeProfileAnswerData($question, $answer, int $score = 0, string $comment = ''): array
    {
        $question = $question->toArray();
        if (!is_array($answer)) {
            $answer = [$answer];
        }
        return [
            'question' => $question,
            'profileAnswer' => $answer,
            'profileScore' => $score,
            'comment' => $comment
        ];
    }

    private function evaluateTextAnswer($question, $answer)
    {
        // 假设有一个 AI 接口
        $score = $this->callAIAnswerEvaluation($question->id, $answer);
        return $score;
    }
}