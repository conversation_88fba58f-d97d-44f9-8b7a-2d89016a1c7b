<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PaymentEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $userId;//用户id
    public $selfBusinessCommission;//自身业务分成
    public $KpiManagementAllowance;//KPI管理津贴
    public $userType;//用户类型 0:合伙人 1:预备管理合伙人 2:管理合伙人

    /**
     * Create a new event instance.
     */
    public function __construct($userId, $selfBusinessCommission, $KpiManagementAllowance, $userType)
    {
        $this->userId = $userId;
        $this->selfBusinessCommission = $selfBusinessCommission;
        $this->KpiManagementAllowance = $KpiManagementAllowance;
        $this->userType = $userType;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}
