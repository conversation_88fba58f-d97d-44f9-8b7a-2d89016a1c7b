<?php

namespace App\Console\Commands;

use App\Models\ProfileInfoModel;
use App\Services\NotificationServices;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ManagerPowerNotify extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'manager-power-notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '失去晋升管理合伙人资格提前7天提醒';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            //查询七天之后的日期
            $date = date('Y-m-d', strtotime('+7 day'));

            //查询非管理合伙人 且 有晋升资格时间的用户
            $users = ProfileInfoModel::where('role_level', '!=', ProfileInfoModel::ROLE_PARTNER_MANAGER)
                ->whereNotNull('manager_prower_start_time')
                ->whereDate('manager_power_expire_time', $date)
                ->get();
            if (!empty($users)) {
                foreach ($users as $user)
                    //发送通知
                    (new NotificationServices())->send('PromotionTips', ['targetUser' => $user->id, 'date' => $date], $user->toArray());
            }
        } catch (\Exception $e) {
            Log::info('发送【合伙人失去晋升资格】通知失败：', [$e->getMessage()]);
        }

    }
}
