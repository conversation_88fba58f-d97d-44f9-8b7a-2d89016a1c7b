<?php

namespace App\Http\Controllers\Api\v1\Payment;

use App\Http\Controllers\Controller;
use App\Models\DocumentFoldersModel;
use App\Models\Invite;
use App\Models\PaymentModel;
use App\Models\ProfileAdvanceCommissionLogModel;
use App\Models\ProfileAdvanceCommissionModel;
use App\Models\ProjectCategoryModel;
use App\Models\ProjectServiceModel;
use App\Services\OssService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\DocumentInfoModel;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class PaymentController extends Controller
{

    /**
     * describe：收支明细列表
     * index
     * @param Request $request
     * @return null
     * 2025/6/19 - Mark
     */
    public function index(Request $request)
    {
        $data = $request->validate([
            'type' => 'nullable|integer|in:1,2,3',
            'page_size' => 'nullable|integer|min:1',
            'start_time' => 'nullable|date_format:Y-m-d H:i:s',
            'end_time' => 'nullable|date_format:Y-m-d H:i:s',
        ]);
        $user = $request->attributes->get('user');
        $list = PaymentModel::with(['project', 'projectContent'])
            ->where('profileID', $user['profileID'])
            ->when($request->type, function ($query, $type) {
                if ($type == 1) { // 系统的收入  个人的支出
                    $query->whereIn('type', [PaymentModel::TYPE_INCOME, PaymentModel::TYPE_COMMISSION_EXPENSE]);
                } elseif ($type == 2) { // 系统的支出  个人的收入
                    $query->whereIn('type', [PaymentModel::TYPE_EXPENSE, PaymentModel::TYPE_COMMISSION_INCOME]);
                } elseif ($type == 3) {
                    $query->where('type', PaymentModel::TYPE_EXEMPT);
                } else {
                    $query->whereIn('type', [PaymentModel::TYPE_EXPENSE, PaymentModel::TYPE_INCOME, PaymentModel::TYPE_EXEMPT, PaymentModel::TYPE_COMMISSION_EXPENSE, PaymentModel::TYPE_COMMISSION_INCOME]);
                }
            })
            ->when($request->start_time, function ($query, $start_time) {
                $query->where('createtime', '>=', $start_time);
            })
            ->when($request->end_time, function ($query, $end_time) {
                $query->where('createtime', '<=', $end_time);
            })
            ->orderBy('createtime', 'desc')
            ->paginate($data['page_size'] ?? 10);

        foreach ($list as &$item) {
            // 交易金额
            if (in_array($item['type'], [PaymentModel::TYPE_INCOME, PaymentModel::TYPE_EXEMPT, PaymentModel::TYPE_COMMISSION_EXPENSE, PaymentModel::TYPE_COMMISSION_INCOME])) {
                $item['amount'] = $item['put_amount'];
            } else { // 支出
                $item['amount'] = $item['fee'];
            }
            // 业务类型
            $item['project_name'] = $item['project']['projectCategoriesNameZH'];

            unset($item['projectContent']);
            unset($item['project']);
        }
        return responseSuccess($list);
    }

    /**
     * 账单列表
     * @return void
     */
    public function index_old(Request $request)
    {
        $body = $request->validate([
            'type' => ['integer', Rule::in([1, 2])],
            'page_size' => ['integer', 'min:1'],
            'start_time' => ['date_format:Y-m-d H:i:s'],
            'end_time' => ['date_format:Y-m-d H:i:s'],
        ]);
        $pageSize = $body['page_size'] ?? 10;
        $user = $request->attributes->get('user');
        $data = PaymentModel::with(['project', 'projectContent'])->where('profileID', $user['profileID']);
        if ($body['type']) {
            $data = $data->where('type', $body['type']);
        }
        if ($body['start_time']) {
            $data = $data->where('createtime', '>=', $body['start_time']);
        }
        if ($body['end_time']) {
            $data = $data->where('createtime', '<=', $body['end_time']);
        }
        $data = $data->orderBy('createtime', 'desc')->paginate($pageSize);
        if (empty($data)) {
            return responseSuccess();
        }

        foreach ($data as &$item) {

            // 交易金额
            if ($item['type'] == 1) { // 收入
                $item['amount'] = $item['divide_profit'];
            } else { // 支出
                $item['amount'] = $item['fee'];
            }
            // 业务类型
            $item['project_name'] = $item['project']['projectCategoriesNameZH'];

            unset($item['projectContent']);
            unset($item['project']);
        }
        return responseSuccess($data);
    }

    public function total(Request $request)
    {
        $user = $request->attributes->get('user');

        // 获取当前月的第一天和最后一天
        $startOfMonth = Carbon::now()->startOfMonth()->toDateString();
        $endOfMonth = Carbon::now()->endOfMonth()->toDateString();

        // 查询当前月的收入
        $income = PaymentModel::where('type', 2)
            ->where('profileID', $user['profileID'])
            ->whereBetween('createtime', [$startOfMonth, $endOfMonth])
            ->sum('divide_profit');

        // 查询当前月的支出
        $expense = PaymentModel::where('type', 1)
            ->where('profileID', $user['profileID'])
            ->whereBetween('createtime', [$startOfMonth, $endOfMonth])
            ->sum('fee');

        // 计算同比昨日增加
        $yesterday = PaymentModel::getTodayIncomeAndExpense($user['profileID']);

        // 总流水
        $total = bcadd($income, $expense, 2);

        //待抵扣金额
        $logs = ProfileAdvanceCommissionModel::where('profile_id', $user['profileID'])->get();
        $advancePriceSum = $logs->sum('advance_price');
        $recoupPriceSum = $logs->sum('recoup_price');
        $deductible = $advancePriceSum - $recoupPriceSum;

        return responseSuccess(['income' => $income, 'expense' => $expense, 'total' => $total, 'yesterday' => $yesterday , 'deductible' => $deductible]);
    }

    /**
     * 账单收入
     * @return void
     */
    public function in(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $page_size = $body['page_size'] ?? 10;
        $page = $body['page'] ?? 1;

        $data = PaymentModel::query()->with('projectContent')->where('type', 1)
            ->where('profileID', $user['profileID'])
            ->orderBy('createtime', 'desc')
            ->paginate($page_size);

        $items = $data->items();

        // 合伙人加盟申请项目ID
        $projectId = ProjectCategoryModel::query()->where('type', 1)->value('projectCategoriesID');
        // 合伙人加盟注册(除了加盟申请费)
        $noPayProjectDetail = ProjectServiceModel::query()
            ->where('projectId', $projectId)
            ->where('remark', '<>', 'register.partner.fees')->pluck('id')->toArray();

        $items = collect($items)->map(function ($item) use ($projectId, $noPayProjectDetail) {
            $item->titleZH = $item->projectContent->title_zh;
            $item->titleEN = $item->projectContent->title_en;
            $item->titleZT = $item->projectContent->title_zt;
            $item->titleMS = $item->projectContent->title_ms;
            $item->currency = $item->projectContent->currency;

            if ($item->status == 1 && $item->projectId == $projectId
                && in_array($item->detailId, $noPayProjectDetail)) { // 已付款的合伙人加盟申请项目
                $item->remark = __('payment remark exempted');// 已豁免
            } else if ($item->status == 0 && $item->projectId == $projectId
                && in_array($item->detailId, $noPayProjectDetail)) { // 未付款的合伙人加盟申请项目
                $item->remark = __('payment remark exempt');;// 可豁免
            }
            $item->fee = number_format($item->fee, 2);

            // 发票地址
            $item->invoice_file = OssService::link($item->invoice_file);
            unset($item->projectContent);
            return $item;
        });

        $currentPage = $data->currentPage();
        $perPage = $data->perPage();
        $totalRecord = $data->total();
        $totalPage = $data->lastPage();
        $paginate = [
            'currentPage' => $currentPage,
            'perPage' => $perPage,
            'totalRecord' => $totalRecord,
            'totalPage' => $totalPage,
        ];

        $compact = compact('items', 'paginate');

        return responseSuccess($compact);

    }

    /**
     * 账单支出
     * @return void
     */
    public function out(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $page_size = $body['page_size'] ?? 10;
        $page = $body['page'] ?? 1;

        $data = PaymentModel::query()->with('projectContent')->where('type', 2)
            ->where('profileID', $user['profileID'])
            ->orderBy('createtime', 'desc')
            ->paginate($page_size);

        $items = $data->items();

        // 合伙人加盟申请项目ID
        $projectId = ProjectCategoryModel::query()->where('type', 1)->value('projectCategoriesID');
        // 合伙人加盟注册(除了加盟申请费)
        $noPayProjectDetail = ProjectServiceModel::query()
            ->where('projectId', $projectId)
            ->where('remark', '<>', 'register.partner.fees')->pluck('id')->toArray();

        $items = collect($items)->map(function ($item) use ($projectId, $noPayProjectDetail) {
            $item->titleZH = $item->projectContent->title_zh;
            $item->titleEN = $item->projectContent->title_en;
            $item->titleZT = $item->projectContent->title_zt;
            $item->titleMS = $item->projectContent->title_ms;
            $item->currency = $item->projectContent->currency;

            if ($item->status == 1 && $item->projectId == $projectId
                && in_array($item->detailId, $noPayProjectDetail)) { // 已付款的合伙人加盟申请项目
                $item->remark = __('payment remark exempted');// 已豁免
            } else if ($item->status == 0 && $item->projectId == $projectId
                && in_array($item->detailId, $noPayProjectDetail)) { // 未付款的合伙人加盟申请项目
                $item->remark = __('payment remark exempt');;// 可豁免
            }
            $item->fee = number_format($item->fee, 2);
            // 发票地址
            $item->invoice_file = OssService::link($item->invoice_file);

            unset($item->projectContent);
            return $item;
        });

        $currentPage = $data->currentPage();
        $perPage = $data->perPage();
        $totalRecord = $data->total();
        $totalPage = $data->lastPage();
        $paginate = [
            'currentPage' => $currentPage,
            'perPage' => $perPage,
            'totalRecord' => $totalRecord,
            'totalPage' => $totalPage
        ];

        $compact = compact('items', 'paginate');

        return responseSuccess($compact);

    }

    /**
     * 合伙人分成明细
     * @return void
     */
    public function income(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $page_size = $body['page_size'] ?? 10;
        $page = $body['page'] ?? 1;

        $data = PaymentModel::query()->select('profileID', 'paymentNumber', 'project_name', 'detail_name', 'createtime', 'divide_type',
            'divide_percent', 'divide_amount', 'income_type', 'son_id', 'proxy_fax', 'divide_profit', 'check', 'check_time', 'pay_type')->where('type', 1)
            ->where('profileID', $user['profileID'])
            ->orderBy('createtime', 'desc')
            ->paginate($page_size);

        $items = $data->items();


        $paginate = [
            'currentPage' => $data->currentPage(),
            'perPage' => $data->perPage(),
            'totalRecord' => $data->total(),
            'totalPage' => $data->lastPage()
        ];

        $compact = compact('items', 'paginate');

        return responseSuccess($compact);
    }

    // 上传发票
    public function uploadInvoice(Request $request)
    {
        $user = $request->attributes->get('user');
        $body = $request->all();
        $rules = [
            'id' => ['required'],
            'file' => ['required', 'file', 'mimes:pdf,jpg,jpeg,png,webp', 'max:' . (env('ALLOW_FILE_SIZE') * 1024)],
        ];
        $messages = [
            'file.required' => __('please upload file'),
            'file.mimes' => __('incorrect format img'),
            'file.max' => __('exceed size img', ['limit' => env('ALLOW_FILE_SIZE') . 'M']),
        ];
        $validator = Validator::make($body, $rules, $messages);
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            return responseFail($errors);
        }

        $file = $request->file('file');
        if (empty($file)) {
            return responseFail();
        }

        $resource = $file->store('invoice', 'public');
        if ($resource) {
            $invoice_file = $resource;
        } else {
            return responseFail();
        }

        $result = PaymentModel::where('id', $body['id'])->where('profileID', $user['profileID'])
            ->where('type', 1)
            ->where('check', '<>', 2)->update([
                'check' => 1,
                'invoice_file' => $invoice_file
            ]);
        if ($result !== false) {
            return responseSuccess();
        } else {
            return responseFail();
        }
    }

}
