<?php

namespace App\Listeners;

use App\Events\CheckPartnerEvent;
use App\Models\ProfileInfoModel;
use App\Services\EmailService;
use App\Services\ProfileInfoServices;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Request;

class CheckPartnerListener
{
    private $emailService;
    private $profileInfo;
    private $user;
    private $profileInfoServices;

    /**
     * Create the event listener.
     */
    public function __construct(Request $request, ProfileInfoModel $profileInfoModel, EmailService $emailService, ProfileInfoServices $profileInfoServices)
    {
        //
        $this->request = $request;
        $this->profileInfoModel = $profileInfoModel;
        $this->emailService = $emailService;
        $this->profileInfoServices = $profileInfoServices;
    }

    /**
     * @param CheckPartnerEvent $event
     * @return bool|null
     */
    public function handle(CheckPartnerEvent $event): ?bool
    {
        // 合伙人信息
        $this->profileInfo = $event->profileInfo;
        $status = $event->status;

        // 操作用户ID
        $user = $this->request->attributes->get('user');
        $this->user = $user;

        if ($status == 1) {
            return $this->pass();
        } else if ($status == 2) {
            return $this->refuse();
        }

        return false;
    }

    /**
     * 审核通过
     * @return void
     */
    public function pass() {
        $profileInfo = $this->profileInfo;
        $profileInfoModel = new ProfileInfoModel();
        try{
            $this->profileInfoModel->getConnection()->transaction(function () use ($profileInfo) {
                //ChrisHe 旧代码 start
                //$password = generateRandomString(8);
                $profileInfo->profileRole = 1;
                //$profileInfo->profilePartnerCode = $profileInfoModel->createPartnerCode($profileInfo['profileAddressCountry'], 1);
                $profileInfo->editUser = $this->user['profileID'];
                $profileInfo->editRole = $this->user['profileRole'];
                $profileInfo->editTime = date('Y-m-d H:i:s');
                //$profileInfo->profilePassword = ProfileInfoModel::INITIAL_PASSWORD_PREFIX . hashPassword($password);
                $profileInfo->status = ProfileInfoModel::STATUS_ACTIVE;
                $profileInfo->save();

                // 发送邮件
                $this->emailService->sendPasswordEmail($profileInfo['profileEmail']);
                //ChrisHe 旧代码 end
            });
            return true;
        }catch(\Exception $e){
            Log::error('合伙人审核处理失败', [
                'profileID' => $profileInfo->profileID,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 审核失败
     * @return void
     */
    public function refuse() {

        $profileInfo = $this->profileInfo;

        try {

            // 开启事务
            DB::beginTransaction();

            // 更改状态
            $profileInfo->status = ProfileInfoModel::STATUS_REJECT;
            $profileInfo->save();

            // 站内通知
            $data = [
                'targetUser' =>  $profileInfo['profileID'],
            ];
            \App\Jobs\SendNotice::dispatch($data, 'PartnerRefuse', $this->user)->onQueue('SendNoticeJob');

            // 发送邮件
            $this->emailService->sendCommonEmail($profileInfo['profileEmail'], 'PartnerFailed');


            // 提交
            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            // 记录日志
            Log::info($e->getMessage());

            return false;
        }

    }
}
