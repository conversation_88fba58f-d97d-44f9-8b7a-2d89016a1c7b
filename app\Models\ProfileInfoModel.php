<?php

namespace App\Models;

use App\Models\Course\ProjectServiceCourse;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class ProfileInfoModel extends Model
{
    use HasFactory;

    protected $table = 'cna_profile_info';
    protected $guarded = [];
    protected $primaryKey = 'profileID'; // 自定义主键
    public $incrementing = true; // 表示主键是自增的
    protected $keyType = 'int'; // 主键类型，默认为 'int'

    protected $hidden = [
        'profilePassword'
    ];
    //账号状态
    const STATUS_PAYING = 1; //待付款
    const STATUS_VERIFYING = 2; //待审核
    const STATUS_ACTIVE = 3; //活跃中
    const STATUS_REJECT = 4; //已驳回
    const STATUS_EXPIRE = 5; //已失效
    const INITIAL_PASSWORD_PREFIX = 'X_'; //初始密码固定前缀

    const ROLE_NORMAL = 0;
    const ROLE_PARTNER = 1;
    const ROLE_PARTNER_MANAGER = 2;
    const ROLE_LEVEL_MAP = [
        '',
        '合伙人',
        '管理合伙人'
    ];

    const PROJECT_ROLE_LEVEL = [
        'profile_register' => self::ROLE_NORMAL,
        'profile_upgrade_partner' => self::ROLE_PARTNER,
        'profile_upgrade_partner_manager' => self::ROLE_PARTNER_MANAGER,
    ];

    const ROLE_LEVEL_PROJECT = [
        self::ROLE_NORMAL => 'profile_register',
        self::ROLE_PARTNER => 'profile_upgrade_partner',
        self::ROLE_PARTNER_MANAGER => 'profile_upgrade_partner_manager'
    ];

    public static $statusMap = [
        self::STATUS_PAYING => '待付款',
        self::STATUS_VERIFYING => '待审核',
        self::STATUS_ACTIVE => '活跃中',
        self::STATUS_REJECT => '已驳回',
        self::STATUS_EXPIRE => '已失效',
    ];

    // 用户类型 profileRole 1-合伙人，2-行政，3-律师
    const ROLE_ASSOCIATE = 1;
    const ROLE_ADMIN = 2;
    const ROLE_LAWYER = 3;

    const AVATAR_PATH = 'images/avatar/image-user.svg'; //默认头像

    const ADMIN_PROFILE_ID = 1; //默认公司账号ID

    public function professional()
    {
        return $this->hasMany(ProfessionalUserDataModel::class, 'professionalProfileID', 'profileID');
    }

    public function experience()
    {
        return $this->hasMany(ExperienceUserDataModel::class, 'experienceProfileID', 'profileID');
    }

    public function skill()
    {
        return $this->hasMany(SkillUserDataModel::class, 'skillProfileID', 'profileID');
    }

    // 定义用户和课程的多对多关系
    public function courses()
    {
        return $this->belongsToMany(
            ProjectServiceCourse::class,
            'cna_profile_course',
            'profile_id',
            'project_service_course_id',
            'profileID',
            'id'
        )
            ->withPivot('progress') // 指定中间表的额外字段
            ->withTimestamps();
    }

    public function skills()
    {
        return $this->belongsToMany(
            SkillInfoModel::class,
            'cna_skill_user_data',
            'skillProfileID',
            'skillID',
            'profileID',
            'skillID'
        )
            ->withPivot('skillDescription', 'createTime')
            ->withTimestamps();
    }

    public function professionals()
    {
        return $this->belongsToMany(
            ProfessionalInfoModel::class,
            'cna_professional_user_data',
            'professionalProfileID',
            'professionalID',
            'profileID',
            'professionalID'
        )
            ->withPivot('professionalDescription', 'createTime')
            ->withTimestamps();
    }

    public function experiences()
    {
        return $this->belongsToMany(
            ExperienceInfoModel::class,
            'cna_experience_user_data',
            'experienceProfileID',
            'experienceID',
            'profileID',
            'experienceID'
        )
            ->withPivot('experienceDescription', 'createTime')
            ->withTimestamps();
    }

    public function company()
    {
        return $this->hasMany(CompanyModel::class, 'companyProfileID', 'profileID');
    }

    public function nationality()
    {
        return $this->hasOne(CountryModel::class, 'countryID', 'profileNationalityID');
    }

    /**
     * Method 获取注册者记录
     *
     * @param $email $email [电邮]
     * @param $phone $phone [手机号]
     *
     * @return void
     */
    public function registerRec($email, $phone)
    {
        return self::where('profileEmail', $email)->orWhere('profileContact', $phone)->first();
    }

    /**
     * Method 获取数据记录
     *
     * @param $profileID $profileID [用户id]
     *
     * @return void
     */
    public function getRecord($profileID)
    {
        $data = self::select(
            'profileID',
            'profileName',
            'profileNRIC',
            'profileGender',
            'profileNationalityID',
            'profileBirthDate',
            'profileRole',
            'profileEmail',
            'profileContact',
            'profileAddressUnit',
            'profileAddressStreet',
            'profileAddressDistrictId',
            'profileAddressCityId',
            'profileAddressStateId',
            'profileAddressCountry',
            'profilePartnerCode',
            'profileAvatar',
            'mobilePrefixID',
            'created_at',
            'status'
        )->where('profileID', $profileID)
            ->first();

        return $data;
    }

    public static function existsUserByPhone($phone)
    {
        return self::where('profileContact', $phone)->exists();
    }

    public static function existsUserByEmail($email)
    {
        return self::where('profileEmail', $email)->exists();
    }

    public static function getUserStatus($userID)
    {
        return self::select('status')->where('profileID', $userID)->first()->status;
    }

    /**
     * 生成编码
     * @param $profileAddressCountry 国籍ID
     * @param $role 角色: 合伙人-1，行政-2，律师-3
     * @return string
     */
    public function createPartnerCode($profileAddressCountry, $role)
    {
        // 当前最大编码数
        $curMaxCode = self::query()->where('profileRole', $role)
            ->orderBy('profilePartnerCode', 'desc')
            ->limit(1)
            ->value('profilePartnerCode');
        if ($curMaxCode) {
            $number = preg_replace("/[^0-9]/", "", $curMaxCode);
            $number += 1;
        } else {
            $number = 100000;
        }


        // 中间代号
        $min = '';
        if ($role == 1) {
            $min = 'A';
        } else if ($role == 2) {
            $min = 'S';
        } else if ($role == 3) {
            $min = 'L';
        }

        // 查找用户国际编码
        $pre = CountryModel::where('countryID', $profileAddressCountry)->value('countryISOCode2');
        $code = $pre . $min . $number;

        return $code;
    }

    //检查密码是否正确
    public static function checkPassword($inputPsw, $profilePsw)
    {
        if (str_starts_with($profilePsw, self::INITIAL_PASSWORD_PREFIX)) {
            $profilePsw = substr($profilePsw, strlen(ProfileInfoModel::INITIAL_PASSWORD_PREFIX));
        }
        if ($inputPsw != $profilePsw) {
            return false;
        }
        return true;
    }

    //初始设置完成情况
    public static function initialRes($user)
    {
        $res = [
            'settingLanguage' => 0,
            'profilePassword' => 0,
            'profileNRIC' => 0,
            'bankAccount' => 0,
            'profileContract' => 0,
            'qualification' => 0,
            'business_case' => 0,
        ];
        $setting = Setting::where('profile_id', $user['profileID'])->first();
        if (empty($setting['language'])) {
            $res['settingLanguage'] = 1;
        }
        $nric = Nric::select('id', 'face_file', 'back_file', 'expire_time')
            ->where('profile_id', $user['profileID'])->first();
        if (
            empty($user['profileNRIC']) || !$nric || !$nric['face_file'] || !$nric['back_file'] ||
            ($nric['expire_time'] && $nric['expire_time'] < strtotime('today'))
        ) {
            $res['profileNRIC'] = 1;
        }
        if (str_starts_with($user['profilePassword'], self::INITIAL_PASSWORD_PREFIX)) {
            $res['profilePassword'] = 1;
        }
        $contract = ProfileContractModel::where('profileID', $user['profileID'])
            ->where('type', ProfileContractModel::TYPE_SERVICE)->first();
        if (empty($contract['signature']) || empty($contract['contract'])) {
            $res['profileContract'] = 1;
        }
        $bankCardExpire = BankCardCn::where('profile_id', $user['profileID'])->value('expire_time');
        if (empty($user['bank_account']) || !$bankCardExpire || $bankCardExpire < strtotime('today')) {
            $res['bankAccount'] = 1;
        }
        //针对后来的新用户需要做面试
        if ($user['status'] == self::STATUS_PAYING || $user['status'] == self::STATUS_VERIFYING) {
            $qualification = Qualification::where('profileID', $user['profileID'])->value('education');
            if (!$qualification) {
                $res['qualification'] = 1;
            }
            $businessCase = BusinessCase::where('profileID', $user['profileID'])->first();
            if (empty($businessCase)) {
                $res['business_case'] = 1;
            }
        }
        $res = array_keys(array_filter($res));

        return $res;
    }

    //检查推荐人的三三制团队
    public static function checkRecommendTeam($profileId)
    {
        $user = self::where('profileID', $profileId)->first();
        //是否 非三三制，最低级，下级满3人
        if (!$user || !$user['team_group'] || $user['team_rank'] == 1) {
            return false;
        }
        $underCount = self::where('pre_id', $profileId)
            ->whereIn('status', [self::STATUS_VERIFYING, self::STATUS_ACTIVE])
            ->where('team_group', $user['team_group'])
            ->count();
        if ($underCount >= 3) {
            return false;
        }
        return $user;
    }

    /**
     * 合伙人所属组
     * @param $profileId
     * @return int $groupID (1联盟合伙人; 2管理合伙人; 3三三制)
     */
    public static function getGroupById($profileId)
    {
        $user = self::where('profileID', $profileId)->first();
        if (empty($user)) {
            return false;
        }
        if ($user['team_group']) { // 三三制
            $groupId = 3;
        } else {
            $childs = self::where('pre_id', $profileId)->where('profileRole', 1)->whereIn('status', [self::STATUS_VERIFYING, self::STATUS_ACTIVE])->count();
            if ($childs > 0) {
                $groupId = 2;
            } else {
                $groupId = 1;
            }
        }

        return $groupId;
    }

    /**
     * 管理合伙人数量
     * @param $profileId
     * @return mixed
     */
    public static function getManagerCount($profileId)
    {
        $childs = self::where('pre_id', $profileId)->where('profileRole', 1)->whereIn('status', [self::STATUS_VERIFYING, self::STATUS_ACTIVE])->count();
        return $childs;
    }

    /**
     * 获取三三制合伙人数量
     * @param $profileId
     * @return mixed
     */
    public static function getTeamCount($profileId)
    {
        $allTeam = self::where('team_group', '>', 0)->where('profileRole', 1)->whereIn('status', [self::STATUS_VERIFYING, self::STATUS_ACTIVE])->select('profileID', 'pre_id')->get()->toArray();
        $res = getAllChild($allTeam, $profileId);
        return $res ? count($res) : 0;
    }

    // 行政用户权限
    public static function getAdminPermissions($profileID)
    {
        $profile = self::find($profileID);
        if (!$profile || $profile->profileRole != self::ROLE_ADMIN) {
            return [];
        }

        // todo 超级管理员处理
        // 是超级管理员的话，里面push all, 或者其他处理, 方便前端判断
        return PowerUserDataModel::where('powerProfileID', $profileID)->pluck('powerActionCode')->toArray();
    }

    // 合伙人上传身份证文件
    public function idFile()
    {
        return $this->hasOne(Nric::class, 'profile_id', 'profileID');
    }

    // 合伙人上传银行卡文件
    public function bankFile()
    {
        return $this->hasOne(BankCardCn::class, 'profile_id', 'profileID');
    }

    /**
     * 合伙人合同文件
     */
    public function contractFile()
    {
        return $this->hasOne(ProfileContractModel::class, 'profileID', 'profileID');
    }

    /**
     * 合伙人的推荐人
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'pre_id', 'profileID')
            ->select('profileID', 'profileName', 'profilePartnerCode')
            ->withDefault([
                'profileName' => '无',
                'profilePartnerCode' => '无'
            ]);
    }

    /**
     * 合伙人上传的银行卡信息
     */
    public function bank()
    {
        return $this->belongsTo(BankModel::class, 'bank_id', 'ID')->select('ID', 'bankName');
    }

    /**
     * 合伙人的下级
     */
    public function children()
    {
        return $this->hasMany(self::class, 'pre_id', 'profileID')
            ->select('profileID', 'pre_id', 'profileName', 'profilePartnerCode', 'team_rank');
    }

    /**
     * 合伙人的下级
     */
    public function descendants()
    {
        return $this->children()->with('descendants.teamRank:id,job');
    }

    /**
     * 正常合伙人数据
     */
    public function scopeActivedNormalAssociate($query)
    {
        return $query->where('profileRole', self::ROLE_ASSOCIATE)
            ->where('status', '<>', self::STATUS_PAYING)
            ->where('profileID', '>', 10)
            ->where('profileID', '<>', 15);
    }

    /**
     * 合伙人设置
     */
    public function setting()
    {
        return $this->hasOne(Setting::class, 'profile_id', 'profileID');
    }

    /**
     * 合伙人选项（前端、全部）
     */
    public static function options()
    {
        return self::activedNormalAssociate()->select('profileID', 'profileName', 'profilePartnerCode')->get();
    }

    /**
     * 三三制团队等级
     */
    public function teamRank()
    {
        return $this->belongsTo(TeamRank::class, 'team_rank', 'id');
    }

    /**
     * 合伙人的经验数据
     */
    public function newExperiences()
    {
        return $this->hasMany(ExperienceData::class, 'profile_id', 'profileID');
    }

    /**
     * 行政后台审核合伙人信息状态
     */
    public function adminReviewStatus()
    {
        return $this->hasOne(AdminReviewProfileStatus::class, 'profile_id', 'profileID');
    }

    /**
     * AI 工作站的面试（线下面试，只有一次？）
     */
    public function aiStationInterview()
    {
        return $this->hasOne(Interview::class, 'user_id', 'profileID');
    }

    /**
     * 合伙人所在的行政区划（区）
     * profileAddressStateId profileAddressCityId profileAddressDistrictId
     */
    public function district()
    {
        return $this->belongsTo(DivisionCn::class, 'profileAddressDistrictId', 'id');
    }

    /**
     * 合伙人职业领域
     */
    public function profession()
    {
        return $this->belongsTo(ProfessionModel::class, 'profession_id', 'id');
    }


    public function qualification()
    {
        return $this->hasOne(Qualification::class, 'profileID', 'profileID');
    }

    public function province(){
        return $this->belongsTo(DivisionCn::class, 'profileAddressStateId', 'id');
    }

}
