<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // 定期清理过期通知用户数据
        $schedule->command('notifiy_validity_clean')->everyMinute()->withoutOverlapping();
        $schedule->command('calendar_notify')->everyMinute()->withoutOverlapping();
        //$schedule->command('nric_expire_notify')->dailyAt('1:00')->withoutOverlapping();
        //$schedule->command('bank_card_expire_notify')->dailyAt('1:00')->withoutOverlapping();
        //$schedule->command('user_expire_notify')->dailyAt('1:00')->withoutOverlapping();
        $schedule->command('wechat_check_order')->everyMinute()->withoutOverlapping();
        $schedule->command('profile_setting_notify')->everyMinute()->withoutOverlapping();
        $schedule->command('sync_user')->everyMinute()->withoutOverlapping();
        //每天下午9点 提醒7天后失去晋升管理合伙人的机会
        //$schedule->command('manager-power-notify')->dailyAt('17:00')->withoutOverlapping();
        //每天早上9点 提醒昨天后失去晋升管理合伙人的机会
        //$schedule->command('manager-power-lose-notify')->dailyAt('09:00')->withoutOverlapping();
        //收款通知 每个月最后一天18点提醒
        //$schedule->command('payment-notify')->cron('0 18 L * *')->withoutOverlapping();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
