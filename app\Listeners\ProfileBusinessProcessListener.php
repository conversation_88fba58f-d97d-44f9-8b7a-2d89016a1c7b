<?php

namespace App\Listeners;

use App\Events\ProfileBusinessProcess;
use App\Models\IcbcOrderModel;
use App\Models\PaymentMainModel;
use App\Models\PaymentModel;
use App\Models\ProfileInfoModel;
use App\Models\ProjectCategoryModel;
use App\Models\ProjectServiceDataModel;
use App\Models\ProjectServiceModel;
use App\Services\PaymentServices;
use App\Services\ProfileBusinessProcessServices;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProfileBusinessProcessListener
{
    private $profileBusinessProcessServices;
    private $paymentServices;

    public function __construct(ProfileBusinessProcessServices $profileBusinessProcessServices,PaymentServices $paymentServices)
    {
        $this->profileBusinessProcessServices = $profileBusinessProcessServices;
        $this->paymentServices = $paymentServices;
    }

    public function handle(ProfileBusinessProcess $event): void
    {
        try{
            $orderId = $event->orderId;
            $orderInfo = IcbcOrderModel::find($orderId);
            if(empty($orderInfo)){
                throw new \Exception('订单信息获取失败');
            }
            $orderInfo = $orderInfo->toArray();
            $projectInfo = ProjectServiceModel::find($orderInfo['project_id']);
            if(empty($projectInfo)){
                throw new \Exception('项目信息获取失败');
            }
            //获取用户信息 对应的 业务状态
            $user = ProfileInfoModel::where('profileID',$orderInfo['user_id'])->first();
            if(empty($user)){
                throw new \Exception('合伙人信息获取失败');
            }
            //获取对应进程
            if( !empty($orderInfo['process_id']) ){
                //订单记录的进程id 回溯
                $process = $this->profileBusinessProcessServices->getById($orderInfo['process_id']);
            }else{
                $process = $this->profileBusinessProcessServices->getByProfileID($orderInfo['user_id'],$projectInfo['code']);
            }
            if(empty($process)){
                throw new \Exception('业务进程获取失败');
            }
            $process = $process->toArray();
            if( $process['pay_id'] != $orderId ){//更新实际触发业务的支付单
                $this->profileBusinessProcessServices->update($process['id'],['pay_id'=>$orderId]);
            }
            //收款明细记录
            if ( $orderInfo['pay_status'] ==  1 ) {
                //记录收款明细
                $this->paymentServices->createFromPayOrder($orderInfo,$process,$event->totalAmount);

                //判断结束业务流程
                $this->profileBusinessProcessServices->finished($process);
            }
        }catch(\Exception $e){
            debugLog("监听报错:".$e->getMessage().'|'.json_encode($e->getTrace()));
        }
    }
}