<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExamQuestionNumConfig extends Model
{
    use SoftDeletes;
    protected $table = 'cna_exam_question_num_config';
    protected $guarded = [];


    //时间转换
    protected function serializeDate(\DateTimeInterface $date){
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

}