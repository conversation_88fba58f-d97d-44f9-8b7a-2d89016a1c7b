<?php

namespace App\Services;

use App\Models\ProfileInfoModel;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AiService
{
    const API_DOMAIN = 'https://api.testai.corporate-advisory.cn'; //api域名
    const GEN_TOKEN_PATH = '/api/v1/platform/generateToken'; //生成token

    const AI_UEL = 'https://api.testai.corporate-advisory.cn/train';//AI请求域名
    const AI_PATH = '/api/v1/ai/chat/completions';//请求地址

    public static function generateToken($userProfile)
    {
        $url = self::API_DOMAIN . self::GEN_TOKEN_PATH;
        $avatar = url(storageUrl($userProfile['profileAvatar'] ?: ProfileInfoModel::AVATAR_PATH));
        $payload = [
            'action' => 'platform',
            'platformUserId' => $userProfile['uuid'],
            'platformUserAvatar' => $avatar,
            'platformUserNickname' => $userProfile['profileName'],
            'platformUserEmail' => $userProfile['profileEmail'],
        ];
        $platformToken = JWT::encode($payload, env('AI_ASSISTANT_KEY'), 'HS256');
        $postData = [
            'platform' => '928c853e-a9f0-4344-9418-c523474f0314',
            'platformToken' => $platformToken,
        ];
        $response = Http::post($url, $postData);
        if ($response->successful()) {
            $res = $response->json();
            return $res['data']['token'] ?? false;
        }
        return false;
    }


    /**
     * describe：AI获取简答题分数
     * getShortAnswerScore
     * @param $dataArr
     * @return array|mixed|void
     * 2025/6/21 - Mark
     */
    public function getShortAnswerScore($dataArr)
    {
        $arr = [
            'roleId' => '7c0253c4-0c3f-41bd-ab3b-471388be8d0d',//模型id
            'prompt' => json_encode($dataArr),
        ];
        $url = self::AI_UEL .self::AI_PATH;
        $response = Http::withoutVerifying()->post($url, $arr);
        if ($response->successful()) {
            $res = $response->json();
            if($res['code'] == 200 && $res['message'] == 'success'){
                $content = json_decode($res['data']['content'], true);
                return $content;
            }
        }else{
            Log::info('AI返回简答题分数出错：', [$response]);
            return [
                'score' => 0,
                'comment' => '暂无',
            ];
        }
    }
}