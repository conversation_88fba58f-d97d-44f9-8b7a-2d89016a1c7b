<?php

namespace App\Http\Controllers\Api\v1\ProfileBusiness;

use App\Http\Controllers\Controller;
use App\Models\ProfileBusinessProcessModel;
use App\Models\ProfileInfoModel;
use App\Services\ProfileBusinessProcessServices;
use App\Services\ProjectServiceDataServices;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Date;

class ProcessController extends Controller
{
    public function __construct(ProfileBusinessProcessServices $profileBusinessProcessServices)
    {
        $this->services = $profileBusinessProcessServices;
    }

    public function getUserId($token){
        $decodeToken = JWT::decode($token, new Key(env('JWT_KEY'), 'HS256'));
        if (!isset($decodeToken->action)) {
            throw new \Exception('action not exist');
        }
        if ($decodeToken->action != ACTION_PROFILE_REGISTER) {
            throw new \Exception('action error');
        }
        return $decodeToken->user_id;
    }

    public function index(Request $request)
    {
        $param = $request->validate([
            'project_code' => 'nullable|string',
            'token' => 'nullable|string'
        ]);
        if (!empty($param['token'])) {
            $userId = $this->getUserId($param['token']);
        }else{
            $user = $request->attributes->get('user');
            if( empty($user) ){
                return responseFail('用户信息过期');
            }
            $userId = $user->profileID;
        }
        $projectCode = $param['project_code'] ?? '';
        $process = $this->services->getByProfileID($userId,$projectCode);
        if( empty($process) ) {
            $process = [
                'status' => -1,
                'status_remark' => '当前无业务进程'
            ];
        }
        return responseSuccess($process);
    }

    public function check(Request $request){
        $param = $request->validate([
            'process_order_no' => 'required|string',
            'token' => 'nullable|string'
        ]);
        try{
            if (!empty($param['token'])) {
                $userId = $this->getUserId($param['token']);
                $user = ProfileInfoModel::where('profileID', $userId)->first();
            }else {
                $user = $request->attributes->get('user');
                if (empty($user)) {
                    return responseFail('用户信息过期');
                }
                $userId = $user->profileID;
            }
            $process = $this->services->getByNo($param['process_order_no']);
            if( empty($process) || $process['profileID'] != $userId ) {
                $res['state'] = false;
                return responseSuccess($res,'无此业务进程');
            }
            $message = '';
            $res['is_finished'] = $this->services->check($process['id'],$message) ? 1 : 0;
            //注册特定内容
            if( $process['project_code'] == 'profile_register' && $res['is_finished'] ) {
                $user = ProfileInfoModel::where('profileID', $userId)->first();
                //生成token
                $action = ACTION_PROFILE_AUTH;
                $payload = [
                    'exp' => time() + (3600 * 24 * 7),
                    'iat' => time(),
                    'action' => $action,
                    'user_id' => $user['profileID'],
                ];
                $token = JWT::encode($payload, env('JWT_KEY'), 'HS256');
                setcookie(
                    "Authorization",
                    "Bearer $token",
                    time() + 3600 * 24 * 7,
                    "/"
                );
                $res['token'] = 'Bearer ' . $token;
                $res['register_setting'] = ProfileInfoModel::initialRes($user);
            }
            return responseSuccess($res,$message);
        }catch(\Exception $e){
            return responseFail($e->getMessage());
        }
    }

    public function getProjectCode(Request $request,ProjectServiceDataServices $projectServiceDataServices){
        $param = $request->validate([
            'project_code' => 'nullable|string',
            'project_name' => 'nullable|string',
        ]);
        return $projectServiceDataServices->getCodeList($param);
    }

    /**
     * 新增业务进程
     * @param Request $request
     * @return void
     */
    public function create(Request $request){
        $param = $request->validate([
            'project_code' => 'required|string',
            'token' => 'nullable|string',
        ],[
            'project_code.required' => '业务代码不能为空',
            'project_code.string' => '业务代码不正确',
        ]);
        if (!empty($param['token'])) {
            $userId = $this->getUserId($param['token']);
        }else{
            $user = $request->attributes->get('user');
            if( empty($user) ){
                return responseFail('用户信息过期');
            }
            $userId = $user->profileID;
        }
        //获取最新的数据项  auth 是被处理过的数据项
        $user = ProfileInfoModel::find($userId);
        //查询code可用性
        //进程创建条件 暂时这里是逻辑判断 后续是业务管理是否一次性业务来判断
        switch( $param['project_code'] ) {
            case 'profile_register' :
                if( $user->status > ProfileInfoModel::STATUS_PAYING ){
                    return responseFail('账户状态无需申请注册加盟');
                }
                break;
            case 'profile_upgrade_partner' :
                if( $user->role_level == ProfileInfoModel::ROLE_PARTNER ){
                    return responseFail('已是合伙人，无需重复申请');
                }
                if( $user->role_level == ProfileInfoModel::ROLE_NORMAL && $user->has_manager_power == 1 ){
                    $monthLater = Date::parse($user->manager_prower_start_time)->addMonth();
                    // 计算到一个月后的剩余时间
                    $user->manager_time = $monthLater->startOfDay()->diffInDays(now()->startOfDay()) ?? 0;
                    if( $user->manager_time > 0 ){
                        return responseFail('正在进行管理合伙人晋升流程，无法申请');
                    }

                }
                break;
            case 'profile_upgrade_partner_manager' :
                if( $user->role_level == ProfileInfoModel::ROLE_PARTNER_MANAGER ){
                    return responseFail('已是管理合伙人，无需重复申请');
                }
                //是否有 晋升资格
                if( $user->role_level == ProfileInfoModel::ROLE_NORMAL && $user->has_manager_power == 1 ){
                    $monthLater = Date::parse($user->manager_prower_start_time)->addMonth();
                    // 计算到一个月后的剩余时间
                    $user->manager_time = $monthLater->startOfDay()->diffInDays(now()->startOfDay()) ?? 0;
                    if( $user->manager_time > 0 ){
                        return responseFail('已获得临时管理合伙人权限，无需重复申请');
                    }

                }
                $request->attributes->set('user', $user);
                if( $user->status == ProfileInfoModel::ROLE_PARTNER_MANAGER ){
                    return responseFail('已是管理合伙人，无需重复申请');
                }
                break;
        }
        //检查code可用性
        //检查用户进行中的业务
        $process = $this->services->getByProfileID($userId,$param['project_code']);
        if( !empty($process) && $process->status == ProfileBusinessProcessModel::STATUS_DOING ) {
//            if( $process['project_code'] == $param['project_code'] ){
            return responseSuccess($process,'业务已在办理中');
//            }
            //这里是否允许多业务并行
//            return responseFail('已有业务进行中');
        }
        $process = $this->services->create($userId,$param['project_code']);
        if( !$process ){
            return responseFail('操作失败');
        }
        $returnData = [
            'process_order_no' => $process['process_order_no'],
            'project_code' => $process['project_code'],
            'project_remark' => $process['project_remark'],
            'status_remark' => $process['status_remark']
        ];
        return responseSuccess($returnData);
    }

    /**
     * 新增业务进程
     * @param Request $request
     * @return void
     */
    public function withdraw(Request $request){
        $user = $request->attributes->get('user');
        $param = $request->validate([
            'project_code' => 'required|string',
        ],[
            'project_code.required' => '业务代码不能为空',
            'project_code.string' => '业务代码不正确',
        ]);
        //检查code可用性
        //检查用户进行中的业务
        $business = $this->services->getByProfileID($user->profileID,$param['project_code']);
        if( empty($business) ) {
            return responseFail('没有对应业务在进行');
        }
        $this->services->update($business['id'],['status'=>ProfileBusinessProcessModel::STATUS_CANCEL]);
        return responseSuccess();
    }
}