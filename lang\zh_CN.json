{"param error": ":param 错误", "please upload complete file": "请上传完整文件", "captcha code error": "验证码错误", "account or password error": "账号或密码错误", "login not pay": "为确保申请审核顺利进行，请尽快完成付款。", "login review ing": "您的合伙人申请尚未审核，请耐心等待。审核预计需1-3个工作日，期间请留意电邮通知。", "login reject": "您的合伙人申请审核不通过，请耐心等待退款到账。", "login success": "登录成功", "old password error": "旧密码错误", "register already active": "您的账户已激活，请登录以访问您的账户", "confirm new password": "请确认新密码", "team phone exist": "该电话号码已被注册，您无法重复使用该号码进行注册操作。", "team email exist": "该电邮地址已被注册，您无法重复使用该电邮进行注册操作。", "team phone/email exist": "该电话号码/电邮地址已被注册，您无法重复使用该电邮进行注册操作。", "team phone error": "推荐人手机号错误", "team son out of limit": "该账户已超过3人限制，您无法继续添加新的成员或进行注册操作。", "team recommend not found": "推荐人不存在", "incorrect format img": "请上传正确格式的图片", "exceed size img": "请上传:limit内大小的图片", "image upload failed": "图片上传失败", "please select professional qualification": "请选择专业资格", "please select personal skills": "请选择个人技能", "missing parameter": "缺少:param参数", "order failed": "下单失败", "please upload file": "请上传文件", "incorrect format file": "请上传正确格式的文件", "exceed size file": "文件大小不能超过:limit", "operation failed": "操作失败", "please select value added services": "请选择增值服务", "please upload pre review form": "请上传预审表格", "please upload the pre review form in PDF format": "请上传pdf格式的预审表格", "please select bank": "请选择银行", "please select payment method": "请选择支付方式", "please fill in the payment number": "请填写付款编号", "please upload payment proof": "请上传付款证明", "please select project": "请选择项目", "please fill in the company name": "请填写企业名字", "please fill in the enterprise registration number": "请填写企业注册号码", "please select company category": "请选择企业类别", "please select country": "请选择国籍", "please fill in the correct email": "请正确填写邮箱", "please select phone area code": "请选择手机区号", "please fill in the phone": "请填写电话号码", "please fill in the password": "请输入密码", "missing captcha key": "缺少图形验证码密钥", "please select language": "请选择语言", "please fill in the current password": "请填写当前密码", "please fill in the new password": "请填写新密码", "please fill in the confirmation password": "请填写确认密码", "please fill in the verification code": "请填写验证码", "phone number format error": "手机号格式错误", "email exist": "邮箱地址已存在", "please fill in the name": "请填写姓名", "please fill in the ID number": "请填写身份证号", "please select gender": "请选择性别", "please fill in the birthdate": "请填写出生日期", "please fill in the unit": "请填写单元", "please fill in the street": "请填写街道", "please fill in the district": "请填写区", "please fill in the city": "请填写城市", "please fill in the state": "请填写省", "please fill in the postcode": "请填写邮政编码", "please select address - country": "请选择地址-国家", "sms verification code error": "短信验证码错误", "account error": "账号错误", "save failed": "保存失败", "edit failed": "编辑失败", "delete failed": "删除失败", "info no exist": "信息不存在", "ID number error": "身份证格式错误", "birthdate error": "出生日期格式错误", "birthdate does not match ID card": "出生日期与身份证不一致", "postcode error": "邮政编码格式错误", "benefit delete error": "已经被申请暂不能删除", "ID card photo error": "请上传清晰有效的身份证照片", "incorrect format of ID card expiration date": "身份证有效期限格式错误", "ID card has expired": "身份证已过期", "Authorization has expired": "登录状态过期,请重新登录", "Authorization has logout": "当前账户已退出,请重新登录", "Authorization failed": "认证失败", "Authorization error": "API认证出错", "Manual": "手册", "Not submitted": "没有提交审核", "Check failed": "审核失败", "The role no allow login": "当前角色不允许登录", "User not allowed log in": "用户禁止登录", "Two passwords are inconsistent": "两次输入密码不一致", "Validity less the current time": "有效期小于当前时间", "data no exist": ":param 信息不存在", "passport photo error": "请上传清晰有效的护照照片", "passport has expired": "护照已过期", "incorrect format of passport expiration date": "护照有效期限格式错误", "payment remark exempt": "可豁免", "payment remark deductible": "可抵扣", "payment remark exempted": "已豁免", "payment remark already deducted": "已抵扣", "bank card photo error": "请上传清晰有效的银行储蓄卡照片", "bank card other error": "卡号/效期/银行名字获取失败，请提供其他有效银行卡", "bank card time error": "银行卡有效期识别失败，请提供其他有效银行卡", "bank not support": "暂不支持该银行", "bank card has expired": "银行卡已过期", "bank card number format error": "银行卡号格式错误", "bank card number length error": "银行卡号长度错误", "bank card number invalid": "银行卡号无效", "missing param": "缺少参数 :param", "gsp register paid one": "预审保证金已支付", "gsp register paid two": "企业开户费已支付", "gsp register paid three": "AI账号开通费已支付", "system error": "系统错误,请联系管理员"}