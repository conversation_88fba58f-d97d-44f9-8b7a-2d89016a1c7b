<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectServiceDataModel extends Model
{
    use HasFactory;

    protected $table = 'cna_project_service_data';

    protected $guarded = [];

    const STATUS_CLOSE = 0; // 关闭状态
    const STATUS_OPEN = 1; // 开放状态

    const STATUS_REMARK = [
        self::STATUS_OPEN => '开放中',
        self::STATUS_CLOSE => '关闭中',
    ];

    const UPGRADE_PROJECT_CODE = ['profile_upgrade_partner_manager','profile_upgrade_partner'];
    const UPGRADE_PROJECT_NAME = ['profile_upgrade_partner_manager'=>'管理合伙人','profile_upgrade_partner'=>'合伙人'];

    public function category(){
        return $this->hasOne(ProjectCategoryModel::class,'projectCategoriesID','projectId');
    }

    public function currencyInfo(){
        return $this->belongsTo(CurrencyRatesModel::class, 'currency_id');
    }

    public function paymentMain(){
        return $this->belongsTo(PaymentMainModel::class, 'payment_main_id');
    }

    // 关联课程
    public function courses()
    {
        return $this->hasMany(Course\ProjectServiceCourse::class, 'service_data_id');
    }
}
