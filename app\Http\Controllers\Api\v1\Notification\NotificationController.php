<?php

namespace App\Http\Controllers\Api\v1\Notification;

use App\Http\Controllers\Controller;
use App\Models\NotificationDataModel;
use App\Models\NotificationDel;
use App\Models\NotificationInfoModel;
use App\Services\NotificationServices;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NotificationController extends Controller
{
    private $request;
    private $notificationInfoModel;
    private $notificationServices;
    private $notificationDataModel;

    public function __construct(Request $request,NotificationServices $notificationServices,NotificationInfoModel $notificationInfoModel, NotificationDataModel $notificationDataModel)
    {
        $this->request = $request;
        $this->notificationInfoModel = $notificationInfoModel;
        $this->notificationDataModel = $notificationDataModel;
        $this->notificationServices = $notificationServices;
    }

    public function index()
    {
        $user = $this->request->attributes->get('user');
        $pageSize = $this->request->input('pageSize', 10);
        $keyword = $this->request->input('keyword', '');
        //$status = $this->request->input('status');  // 0 未读 1已读 空全部
        $eventCode = $this->request->input('event_code');
        if ($eventCode && !in_array($eventCode, NotificationInfoModel::EVENT_CODE)) {
            return responseFail();
        }

        // 所有
        $all = $this->notificationInfoModel->allNotice($user);
        // 已读
        $reads = $this->notificationDataModel->reads($user['profileID']);
        // 未读
        $unReads = array_diff($all, $reads);
        $delIds = NotificationDel::where('profileID', $user['profileID'])
            ->pluck('notificationID')->toArray();
        if ($delIds) {
            $all = array_diff($all, $delIds);
        }

        $data = $this->notificationInfoModel::whereIn('notificationID', $all);
            /* where(function ($query) use ($status, $unReads, $reads, $all) {
                if (isset($status) && $status == 0) {
                    $query->whereIn('notificationID', $unReads);
                } else if ($status == 1) {
                    $query->whereIn('notificationID', $reads);
                } else {
                    $query->whereIn('notificationID', $all);
                }
            }); */
        if( !empty($keyword) ){
            $data = $data->where('notificationTitleZH', 'like', '%'.$keyword.'%');
        }
        if ($eventCode) {
            $data = $data->where('eventCode', $eventCode);
        }
        $data = $data->orderByDesc('created_at')->paginate($pageSize);

        $notification = collect($data->items())->map(function ($item) use ($unReads){
            if (is_array($unReads) && in_array($item['notificationID'], $unReads)) {
                $item['notificationState'] = 0;
            } else {
                $item['notificationState'] = 1;
            }
            return $item;
        });

        $currentPage = $data->currentPage();
        $perPage = $data->perPage();
        $totalRecord = $data->total();
        $totalPage = $data->lastPage();
        $paginate = [
            'currentPage' => $currentPage,
            'perPage' => $perPage,
            'totalRecord' => $totalRecord,
            'totalPage' => $totalPage
        ];  
        
        $compact = compact('notification', 'paginate');
        return responseSuccess($compact);
    }

    public function show($id){
        return responseSuccess($this->notificationServices->info($id));
    }

    public function readAll()
    {
        $user = $this->request->attributes->get('user');
        // 所有
        $allIds = $this->notificationInfoModel->allNotice($user);
        // 已读
        $readsIds = $this->notificationDataModel->reads($user['profileID']);
        // 未读
        $unreadIds = array_diff($allIds, $readsIds);

        try {
            // 开启事务
            DB::beginTransaction();

            if ($unreadIds) {
                foreach ($unreadIds as $unreadId) {

                    // step1: 更新查看数量
                    $this->notificationInfoModel->where('notificationID', $unreadId)
                        ->increment('notificationViews', 1);

                    // step2: 用户通知表新增一条记录
                    $this->notificationDataModel->insert([
                        'notificationID'        => $unreadId,
                        'notificationProfileID' => $user['profileID'],
                        'createUser'            => $user['profileID'],
                        'createRole'            => 0,
                        'createTime'            => date('Y-m-d H:i:s'),
                    ]);
                }
            }
            // 提交
            DB::commit();

            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail();
        }

    }

    public function readSingle()
    {
        $user = $this->request->attributes->get('user');
        $notificationID = $this->request->input('notificationID', 0);

        if (!$notificationID) {
            return responseFail();
        }

        // 通知信息
        $notificationInfo = $this->notificationInfoModel::find($notificationID);
        if (!$notificationInfo) {
            return responseFail();
        }

        if ($notificationInfo['notificationTarget'] == 0) { // 表示发给组非个人
            if ($user['profileRole'] != $notificationInfo['notificationDepartment'] && $user['profileName'] != 'admin') {
                return responseFail();
            }
        } else if ($notificationInfo['notificationTarget'] != $user['profileID']) {
            return responseFail();
        }


        try {
            // 开启事务
            DB::beginTransaction();

            // step1: 更新查看数量
            $notificationInfo->notificationViews = $notificationInfo['notificationViews'] + 1;
            $notificationInfo->save();
            $exits = $this->notificationDataModel::where('notificationID',$notificationInfo['notificationID'])->where('notificationProfileID',$user['profileID'])->exists();
            if( !$exits ){
                // step2: 用户通知表新增一条记录
                $this->notificationDataModel->insert([
                    'notificationID'        => $notificationInfo['notificationID'],
                    'notificationProfileID' => $user['profileID'],
                    'createUser'            => $user['profileID'],
                    'createRole'            => 0,
                    'createTime'            => date('Y-m-d H:i:s'),
                ]);
            }

            // 提交
            DB::commit();

            return responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            return responseFail();
        }
    }

    public function notificationStatusNumber()
    {
        $user = $this->request->attributes->get('user');
        // 所有
        $allIds = $this->notificationInfoModel->allNotice($user);
        // 已读
        $readsIds = $this->notificationDataModel->reads($user['profileID']);
        // 未读
        $unreadIds = array_diff($allIds, $readsIds);
        $all['num'] = count($allIds);
        $unread['num'] = count($unreadIds);

        return responseSuccess(compact('all', 'unread'));
    }

    public function del(Request $request)
    {
        $body = $request->validate([
            'notificationID' => ['required', 'integer', 'min:1'],
        ]);
        $record = NotificationInfoModel::where('notificationID', $body['notificationID'])->first();
        if (empty($record)) {
            return responseSuccess();
        }
        $user = $request->attributes->get('user');
        if ($record['notificationTarget'] == $user['profileID']) {
            NotificationInfoModel::where('notificationID', $body['notificationID'])->delete();
        } else {
            NotificationDel::create([
                'profileID' => $user['profileID'],
                'notificationID' => $body['notificationID'],
            ]);
        }
        NotificationDataModel::where('notificationID', $body['notificationID'])
            ->where('notificationProfileID', $user['profileID'])->delete();

        return responseSuccess();
    }
}
