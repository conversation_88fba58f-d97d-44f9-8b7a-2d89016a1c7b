<?php

namespace App\Http\Controllers\Api\v1\Business;

use App\Models\Course\ProjectServiceCourse;
use App\Models\Course\ProjectServiceCourseIntro;
use App\Models\ProfileInfoModel;
use App\Models\ProjectCategoryModel;
use App\Services\Business\BusinessService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Tymon\JWTAuth\Facades\JWTAuth;
use Ty<PERSON>\JWTAuth\JWT;

class BusinessController
{
    private $service;

    public function __construct()
    {
        $this->service = new BusinessService();
    }

    /**
     * describe：获取指定业务的课程介绍
     * courseIntro
     * @param Request $request
     * @return null
     * 2025/6/14 - Mark
     */
    public function courseIntro(Request $request)
    {
        $data = $request->validate([
            'service_data_id' => 'required|exists:cna_project_service_data,id'
        ]);
        $info = (new ProjectServiceCourseIntro())
            ->where('service_data_id', $data['service_data_id'])
            ->first();
        return responseSuccess($info);
    }

    /**
     * describe：获取用户指定业务的课程
     * course
     * @param Request $request
     * @return null
     * 2025/6/14 - Mark
     */
    public function course(Request $request)
    {
        $user = $request->attributes->get('user');
        $data = $request->validate([
            'service_data_id' => 'required|exists:cna_project_service_data,id',
            'pageSize' => 'nullable'
        ]);
        $list = $this->service->getCourse($user,$data);

        return $list;
    }

    /**
     * describe：获取课程详情
     * courseDetail
     * @param Request $request
     * @return null
     * 2025/6/17 - Mark
     */
    public function courseDetail(Request $request)
    {
        $data = $request->validate([
            'course_id' => 'required|exists:cna_project_service_course,id'
        ]);
        $user = $request->attributes->get('user');
        $detail = $this->service->getCourseDetail($user,$data);
        return $detail;
    }

    /**
     * describe：生成用户课程学习进度
     * addProfileCourseProgress
     * @param Request $request
     * @return void
     * 2025/6/14 - Mark
     */
    public function addProfileCourseProgress(Request $request)
    {
        $data = $request->validate([
            //'profile_id' => 'required|exists:cna_profile_info,profileID',
            'detail' => 'required|array',
            'detail.*.project_service_course_id' => 'required|exists:cna_project_service_course,id',
            'detail.*.progress' => 'required|integer|min:0|max:100'
        ]);
        $user = $request->attributes->get('user');
        $info = $this->service->addProgress($user,$data);
        return $info;
    }

    /**
     * describe：资质上传
     * certificateUpload
     * @param Request $request
     * @return null
     * 2025/6/17 - Mark
     */
    public function certificateUpload(Request $request){
        $data = request()->validate([
            'project_service_data_id' => 'required|exists:cna_project_service_data,id',
            'certificate' => 'required|file|mimes:pdf|max:2048'
        ]);
        $user = $request->attributes->get('user');
        $info = $this->service->certificateUpload($user,$data);
        return $info;
    }
}